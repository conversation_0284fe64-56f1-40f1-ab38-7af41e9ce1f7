/*
 * CSS Unifié pour l'application DCOP 413
 * Fusion de tous les styles personnalisés (hors Tailwind)
 * Version optimisée et consolidée
 */

/* ===== VARIABLES CSS GLOBALES ===== */
:root {
  /* Couleurs principales */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  
  /* Couleurs grises */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* Couleurs de statut */
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  
  /* Ombres */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Transitions */
  --transition: all 0.2s ease-in-out;
  --transition-fast: all 0.15s ease-in-out;
  --transition-slow: all 0.3s ease-in-out;
  
  /* Rayons de bordure */
  --radius-sm: 0.375rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
  
  /* Espacements */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
}

/* ===== RESET ET STYLES DE BASE ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
  height: 100%;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
}

/* ===== UTILITAIRES GÉNÉRAUX ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.hidden {
  display: none !important;
}

.transition-all {
  transition: var(--transition);
}

.transition-fast {
  transition: var(--transition-fast);
}

.transition-slow {
  transition: var(--transition-slow);
}

/* ===== HEADER MODERNE ===== */
.ultra-modern-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: var(--spacing-lg) var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.ultra-modern-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
  pointer-events: none;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.header-top-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
}

.header-logo-section img {
  height: 70px;
  width: auto;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
}

.header-title-section {
  text-align: center;
  flex: 1;
  margin: 0 var(--spacing-lg);
}

.header-main-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-subtitle {
  font-size: 1.125rem;
  opacity: 0.9;
  font-weight: 400;
}

.header-visitor-photo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}

.visitor-photo-container {
  width: 120px;
  height: 120px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255,255,255,0.2);
}

.visitor-photo-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255,255,255,0.8);
  font-size: 0.875rem;
  text-align: center;
}

.photo-icon {
  font-size: 2rem;
  margin-bottom: var(--spacing-xs);
}

.header-nav-section {
  display: flex;
  justify-content: flex-end;
  margin-top: var(--spacing-md);
}

.header-nav-buttons {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.header-nav-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(255,255,255,0.1);
  color: white;
  text-decoration: none;
  border-radius: var(--radius);
  font-weight: 500;
  transition: var(--transition);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
}

.header-nav-btn:hover {
  background: rgba(255,255,255,0.2);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.header-nav-btn.deconnexion {
  background: rgba(239, 68, 68, 0.2);
  border-color: rgba(239, 68, 68, 0.3);
}

.header-nav-btn.deconnexion:hover {
  background: rgba(239, 68, 68, 0.3);
}

/* ===== CONTENEURS PRINCIPAUX ===== */
.modern-form-container-with-header {
  min-height: calc(100vh - 200px);
  background: var(--gray-50);
  padding: var(--spacing-xl);
}

.modern-form-wrapper {
  max-width: 1000px;
  margin: 0 auto;
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
}

.ultra-form-section {
  padding: var(--spacing-2xl);
}

.ultra-section-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.ultra-section-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-sm);
}

.ultra-section-description {
  font-size: 1.125rem;
  color: var(--gray-600);
}

/* ===== GRILLES DE FORMULAIRE ===== */
.ultra-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.ultra-field-full {
  grid-column: 1 / -1;
}

.ultra-field-half {
  grid-column: span 1;
}

/* ===== CHAMPS DE FORMULAIRE ===== */
.ultra-field-group {
  margin-bottom: var(--spacing-lg);
}

.ultra-label {
  display: block;
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: var(--spacing-sm);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.ultra-input,
.ultra-select,
.ultra-textarea {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
  font-size: 1rem;
  transition: var(--transition);
  background: white;
}

.ultra-input:focus,
.ultra-select:focus,
.ultra-textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.ultra-textarea {
  resize: vertical;
  min-height: 100px;
}

/* ===== BOUTONS ===== */
.ultra-form-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
  margin-top: var(--spacing-2xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--gray-200);
}

.ultra-btn {
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  text-decoration: none;
  text-align: center;
  min-width: 160px;
  justify-content: center;
}

.ultra-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.ultra-btn-submit {
  background: linear-gradient(135deg, var(--success-500), var(--success-600));
  color: white;
  box-shadow: var(--shadow-md);
}

.ultra-btn-submit:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.ultra-btn-print {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  box-shadow: var(--shadow-md);
}

.ultra-btn-print:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.ultra-btn-cancel {
  background: linear-gradient(135deg, var(--gray-500), var(--gray-600));
  color: white;
  box-shadow: var(--shadow-md);
}

.ultra-btn-cancel:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* ===== MESSAGES ET NOTIFICATIONS ===== */
.ultra-success-message,
.ultra-error-message {
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  font-weight: 500;
}

.ultra-success-message {
  background: var(--success-50);
  color: var(--success-600);
  border: 1px solid var(--success-200);
}

.ultra-error-message {
  background: var(--error-50);
  color: var(--error-600);
  border: 1px solid var(--error-200);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .header-top-row {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
  }
  
  .header-title-section {
    margin: 0;
  }
  
  .header-main-title {
    font-size: 2rem;
  }
  
  .ultra-form-grid {
    grid-template-columns: 1fr;
  }
  
  .ultra-form-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .ultra-btn {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .ultra-modern-header {
    padding: var(--spacing-md);
  }

  .modern-form-container-with-header {
    padding: var(--spacing-md);
  }

  .ultra-form-section {
    padding: var(--spacing-lg);
  }

  .header-main-title {
    font-size: 1.75rem;
  }

  .visitor-photo-container {
    width: 100px;
    height: 100px;
  }
}

/* ===== STYLES SPÉCIALISÉS LOGIN ===== */
.ultra-login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
}

.ultra-login-card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  padding: var(--spacing-2xl);
  width: 100%;
  max-width: 400px;
}

.ultra-login-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.ultra-login-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-sm);
}

.ultra-login-subtitle {
  color: var(--gray-600);
  font-size: 1rem;
}

.ultra-login-form {
  space-y: var(--spacing-lg);
}

.ultra-forgot-link {
  display: block;
  text-align: center;
  color: var(--primary-600);
  text-decoration: none;
  font-size: 0.875rem;
  margin-top: var(--spacing-md);
  transition: var(--transition);
}

.ultra-forgot-link:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

.ultra-btn-login {
  width: 100%;
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  padding: var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-md);
}

.ultra-btn-login:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.ultra-btn-login:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* ===== STYLES SPÉCIALISÉS ACCUEIL ===== */
.ultra-welcome-section {
  text-align: center;
  padding: var(--spacing-2xl);
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--spacing-xl);
}

.ultra-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.ultra-stat-card {
  background: white;
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  text-align: center;
  transition: var(--transition);
}

.ultra-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.ultra-stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-600);
  margin-bottom: var(--spacing-sm);
}

.ultra-stat-label {
  color: var(--gray-600);
  font-weight: 500;
}

/* ===== STYLES PHOTO ===== */
.ultra-photo-container {
  max-width: 600px;
  margin: 0 auto;
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
}

.ultra-camera-section {
  padding: var(--spacing-xl);
  text-align: center;
}

.ultra-video-container {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-md);
}

.ultra-video {
  width: 100%;
  height: auto;
  display: block;
}

.ultra-status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-sm);
  background: var(--gray-100);
  border-radius: var(--radius);
}

.ultra-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--gray-400);
}

.ultra-status-text {
  font-size: 0.875rem;
  color: var(--gray-600);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in {
  animation: slideIn 0.4s ease-out;
}

/* ===== UTILITAIRES D'ÉTAT ===== */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error {
  border-color: var(--error-500) !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.success {
  border-color: var(--success-500) !important;
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1) !important;
}
