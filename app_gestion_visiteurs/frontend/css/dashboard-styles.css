/*
 * Styles pour le Tableau de Bord Temps Réel - DCOP 413
 * Design ultra-professionnel avec animations et métriques
 */

/* ===== CONTENEUR PRINCIPAL ===== */
.dashboard-container {
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.dashboard-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.dashboard-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.refresh-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px -3px rgba(59, 130, 246, 0.4);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* ===== GRILLE DE MÉTRIQUES ===== */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metric-card {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.metric-card:hover::before {
  opacity: 1;
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 25px -5px rgba(0, 0, 0, 0.15);
}

.metric-card.updated {
  animation: cardPulse 0.5s ease;
}

@keyframes cardPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

.metric-value {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 0.5rem;
  display: block;
}

.metric-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-icon {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  font-size: 2rem;
  opacity: 0.3;
}

/* ===== STATUT DE SÉCURITÉ ===== */
.security-status {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 16px;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

.security-status.secure {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.security-status.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.security-icon {
  font-size: 2.5rem;
}

.security-text {
  flex: 1;
}

.security-level {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.security-details {
  font-size: 0.875rem;
  opacity: 0.9;
}

/* ===== GRAPHIQUES ===== */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.chart-container {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.chart-placeholder {
  text-align: center;
  padding: 2rem;
}

.chart-placeholder h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
}

.chart-data {
  display: flex;
  justify-content: space-around;
  align-items: end;
  height: 200px;
  gap: 1rem;
}

.chart-bar {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 0.5rem;
  border-radius: 8px 8px 0 0;
  min-width: 60px;
  display: flex;
  align-items: end;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.chart-bar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

/* ===== ÉVÉNEMENTS DE SÉCURITÉ ===== */
.security-events-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.event-item {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  border-left: 4px solid;
}

.event-item.low {
  background: #f0fdf4;
  border-color: #10b981;
  color: #065f46;
}

.event-item.medium {
  background: #fffbeb;
  border-color: #f59e0b;
  color: #92400e;
}

.event-item.high {
  background: #fef2f2;
  border-color: #ef4444;
  color: #991b1b;
}

/* ===== MÉTRIQUES DE PERFORMANCE ===== */
.performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.perf-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.perf-label {
  font-weight: 600;
  color: #374151;
  min-width: 60px;
}

.perf-bar {
  flex: 1;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.perf-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #3b82f6);
  border-radius: 4px;
  transition: width 0.5s ease;
}

.perf-value {
  font-weight: 600;
  color: #374151;
  min-width: 40px;
  text-align: right;
}

/* ===== FOOTER DU TABLEAU DE BORD ===== */
.dashboard-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
}

.last-refresh {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.auto-update-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #374151;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 1rem;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .dashboard-controls {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-data {
    height: 150px;
  }
  
  .dashboard-footer {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .dashboard-title {
    font-size: 1.5rem;
  }
  
  .metric-value {
    font-size: 2rem;
  }
  
  .metric-card {
    padding: 1.5rem;
  }
  
  .chart-container {
    padding: 1.5rem;
  }
  
  .security-status {
    padding: 1rem;
  }
  
  .security-icon {
    font-size: 2rem;
  }
}

/* ===== ANIMATIONS AVANCÉES ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.metric-card {
  animation: fadeInUp 0.5s ease;
}

.metric-card:nth-child(1) { animation-delay: 0.1s; }
.metric-card:nth-child(2) { animation-delay: 0.2s; }
.metric-card:nth-child(3) { animation-delay: 0.3s; }
.metric-card:nth-child(4) { animation-delay: 0.4s; }
.metric-card:nth-child(5) { animation-delay: 0.5s; }
.metric-card:nth-child(6) { animation-delay: 0.6s; }

/* ===== THÈME SOMBRE (OPTIONNEL) ===== */
@media (prefers-color-scheme: dark) {
  .dashboard-container {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  }
  
  .metric-card,
  .chart-container,
  .dashboard-header,
  .dashboard-footer {
    background: #1e293b;
    color: #f1f5f9;
  }
  
  .metric-value {
    color: #f1f5f9;
  }
  
  .metric-label {
    color: #94a3b8;
  }
  
  .dashboard-title {
    color: #f1f5f9;
  }
}
