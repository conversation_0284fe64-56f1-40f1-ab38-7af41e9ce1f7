/*
 * Styles pour les fonctionnalités app-core.js
 * Extraction du CSS inline pour respecter les bonnes pratiques
 */

/* ===== NOTIFICATIONS AVANCÉES ===== */
.notification-advanced {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 400px;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.notification-advanced.show {
  transform: translateX(0);
}

.notification-advanced .notification-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.notification-advanced .notification-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.notification-advanced .notification-message {
  font-weight: 500;
  flex: 1;
}

.notification-advanced .notification-close {
  margin-left: auto;
  color: rgba(107, 114, 128, 1);
  transition: color 0.2s ease;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-advanced .notification-close:hover {
  color: rgba(75, 85, 99, 1);
}

.notification-advanced .notification-close svg {
  width: 1rem;
  height: 1rem;
}

/* ===== ERREURS DE CHAMPS AVANCÉES ===== */
.field-error-advanced {
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

/* ===== ÉTATS DE CHAMPS AVANCÉS ===== */
.field-success {
  border-color: #10b981 !important;
  background-color: #f0fdf4 !important;
}

.field-error-state {
  border-color: #ef4444 !important;
  background-color: #fef2f2 !important;
}

/* ===== PHOTO MANAGEMENT ===== */
.photo-visible {
  display: block !important;
}

.photo-hidden {
  display: none !important;
}

.photo-placeholder-visible {
  display: flex !important;
}

.photo-placeholder-hidden {
  display: none !important;
}

.remove-photo-btn-visible {
  display: inline-flex !important;
}

.remove-photo-btn-hidden {
  display: none !important;
}

/* ===== CAMERA SECTION ===== */
.camera-section-visible {
  display: block !important;
}

.camera-section-hidden {
  display: none !important;
}

/* ===== TRANSITIONS DE PAGE ===== */
.page-transition-out {
  transition: opacity 0.4s ease-out, transform 0.4s ease-out;
  opacity: 0.8;
  transform: scale(0.98);
}

.page-transition-in {
  opacity: 0;
  transform: scale(1.02);
  transition: opacity 0.5s ease-out, transform 0.5s ease-out;
}

.page-transition-in.loaded {
  opacity: 1;
  transform: scale(1);
}

/* ===== LOGOUT TRANSITION ===== */
.logout-transition {
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
  opacity: 0.7;
  transform: scale(0.98);
}

/* ===== CHECKBOX SÉCURITÉ ===== */
.security-checkbox-checked {
  color: #10b981 !important;
  font-weight: 600 !important;
}

.security-checkbox-unchecked {
  color: #374151 !important;
  font-weight: 500 !important;
}

/* ===== ENGAGEMENT SECTION ===== */
.engagement-container {
  transition: all 0.3s ease;
}

.engagement-container.checked {
  border-color: #0284c7 !important;
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(14, 165, 233, 0.3) !important;
}

.engagement-container.unchecked {
  border-color: #0ea5e9 !important;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
  transform: translateY(0) !important;
  box-shadow: none !important;
}

/* ===== ANIMATIONS ===== */
@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeOutScale {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

.slide-in-right {
  animation: slideInFromRight 0.3s ease-out;
}

.slide-out-right {
  animation: slideOutToRight 0.3s ease-out;
}

.fade-in-scale {
  animation: fadeInScale 0.4s ease-out;
}

.fade-out-scale {
  animation: fadeOutScale 0.3s ease-out;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .notification-advanced {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .notification-advanced .notification-content {
    padding: 0.875rem;
  }
  
  .notification-advanced .notification-icon {
    font-size: 1.125rem;
  }
  
  .notification-advanced .notification-message {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .notification-advanced {
    top: 5px;
    right: 5px;
    left: 5px;
  }
  
  .notification-advanced .notification-content {
    padding: 0.75rem;
    gap: 0.5rem;
  }
  
  .notification-advanced .notification-icon {
    font-size: 1rem;
  }
  
  .notification-advanced .notification-message {
    font-size: 0.8125rem;
  }
  
  .field-error-advanced {
    font-size: 0.8125rem;
  }
}
