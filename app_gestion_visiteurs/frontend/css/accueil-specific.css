/*
 * Styles spécifiques pour la page d'accueil
 * Extraction du CSS inline pour respecter les bonnes pratiques
 */

/* ===== STATISTIQUES ===== */
.stats-card-visitors {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
}

.stats-card-monthly {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
}

.stats-card-security {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
}

.stats-card-icon {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.stats-card-number {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stats-card-label {
  font-size: 0.875rem;
  opacity: 0.9;
}

/* ===== ACTIONS RAPIDES ===== */
.actions-section {
  text-align: center;
  margin-bottom: 2rem;
}

.new-visitor-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1.125rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  cursor: pointer;
  text-decoration: none;
}

.new-visitor-btn:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
  color: white;
}

.new-visitor-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

.action-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  text-decoration: none;
  color: #374151;
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  color: #374151;
  text-decoration: none;
}

.action-card:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.action-card-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.action-card-content {
  flex: 1;
}

.action-card-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: inherit;
}

.action-card-description {
  font-size: 0.875rem;
  color: #64748b;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .stats-card-visitors,
  .stats-card-monthly,
  .stats-card-security {
    padding: 1.5rem;
  }
  
  .stats-card-icon {
    font-size: 2rem;
  }
  
  .stats-card-number {
    font-size: 1.75rem;
  }
  
  .new-visitor-btn {
    width: 100%;
    justify-content: center;
  }
  
  .action-card {
    padding: 1rem;
  }
  
  .action-card-icon {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .stats-card-visitors,
  .stats-card-monthly,
  .stats-card-security {
    padding: 1rem;
  }
  
  .stats-card-icon {
    font-size: 1.75rem;
  }
  
  .stats-card-number {
    font-size: 1.5rem;
  }
  
  .stats-card-label {
    font-size: 0.75rem;
  }
  
  .new-visitor-btn {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
  
  .action-card {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
  
  .action-card-icon {
    font-size: 1.5rem;
  }
}
