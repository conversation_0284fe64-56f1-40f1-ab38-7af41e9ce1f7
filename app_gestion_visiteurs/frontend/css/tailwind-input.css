@tailwind base;
@tailwind components;
@tailwind utilities;

/* Variables CSS personnalisées pour la cohérence */
:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --success-color: #059669;
  --success-hover: #047857;
  --warning-color: #d97706;
  --danger-color: #dc2626;
  --danger-hover: #b91c1c;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --border-radius: 0.75rem;
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --transition: all 0.2s ease-in-out;
}

/* Composants personnalisés */
@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition duration-200;
  }
  
  .btn-success {
    @apply bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-lg transition duration-200;
  }
  
  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg transition duration-200;
  }
  
  .form-input {
    @apply w-full border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200;
  }
  
  .form-section {
    @apply bg-gray-50 p-6 rounded-xl border border-gray-200 mb-6;
  }
  
  .section-title {
    @apply text-2xl font-semibold text-gray-800 mb-6 flex items-center gap-3;
  }
  
  .badge-number {
    @apply w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-white;
  }
}

/* Utilitaires personnalisés */
@layer utilities {
  .page-break-avoid {
    page-break-inside: avoid;
  }
  
  .hidden-print {
    @media print {
      display: none !important;
    }
  }
}
