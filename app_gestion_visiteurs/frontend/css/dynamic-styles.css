/*
 * Styles dynamiques pour les éléments créés par JavaScript
 * Extraction du CSS inline pour respecter les bonnes pratiques
 */

/* ===== NOTIFICATIONS ===== */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  pointer-events: none;
}

.notification {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transform: translateX(100%);
  transition: transform 0.3s ease;
  pointer-events: auto;
  max-width: 300px;
  word-wrap: break-word;
  font-weight: 500;
  font-size: 0.875rem;
}

.notification.show {
  transform: translateX(0);
}

.notification-success {
  background: #10b981;
  color: #ffffff;
}

.notification-error {
  background: #ef4444;
  color: #ffffff;
}

.notification-warning {
  background: #f59e0b;
  color: #ffffff;
}

.notification-info {
  background: #3b82f6;
  color: #ffffff;
}

/* ===== ERREURS DE CHAMPS ===== */
.field-error {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 4px;
  font-weight: 500;
}

.field-error::before {
  content: '⚠️ ';
  margin-right: 4px;
}

/* ===== ÉTATS DE CHAMPS ===== */
.ultra-input.error,
.ultra-select.error,
.ultra-textarea.error {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.ultra-input.success,
.ultra-select.success,
.ultra-textarea.success {
  border-color: #10b981 !important;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
}

/* ===== ÉTATS DE CHARGEMENT ===== */
.loading {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ===== TRANSITIONS D'ÉLÉMENTS ===== */
.element-transition {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.element-transition.scale-down {
  transform: scale(0.95);
  opacity: 0.8;
}

/* ===== PHOTO PREVIEW ===== */
.photo-preview-hidden {
  display: none !important;
}

.photo-preview-visible {
  display: block !important;
}

.photo-placeholder-hidden {
  display: none !important;
}

.photo-placeholder-visible {
  display: block !important;
}

/* ===== ACTIONS PHOTO ===== */
.photo-actions-container {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.photo-actions-container .ultra-btn {
  min-width: 140px;
}

/* ===== INDICATEURS DE STATUT ===== */
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.status-indicator.success {
  background-color: #10b981;
}

.status-indicator.error {
  background-color: #ef4444;
}

.status-indicator.warning {
  background-color: #f59e0b;
}

.status-indicator.info {
  background-color: #3b82f6;
}

/* ===== ANIMATIONS D'ENTRÉE ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.4s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .notification-container {
    top: 10px;
    right: 10px;
    left: 10px;
  }
  
  .notification {
    max-width: none;
  }
  
  .photo-actions-container {
    flex-direction: column;
    align-items: center;
  }
  
  .photo-actions-container .ultra-btn {
    width: 100%;
    max-width: 280px;
  }
}

@media (max-width: 480px) {
  .notification-container {
    top: 5px;
    right: 5px;
    left: 5px;
  }
  
  .notification {
    padding: 10px 12px;
    font-size: 0.8125rem;
  }
  
  .field-error {
    font-size: 0.8125rem;
  }
}
