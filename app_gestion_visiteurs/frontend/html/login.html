<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Connexion DCOP - 413 Gestion des visiteurs</title>
  <meta name="description" content="Interface de connexion professionnelle et sécurisée">

  <!-- Fonts Google -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

  <!-- Styles -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="/css/professional-design.css" />

  <!-- Scripts -->
  <script src="/js_ts/app-core.js"></script>
</head>

<body class="modern-body">

  <!-- Header ultra-moderne pour page login -->
  <header class="login-header">
    <div class="header-container">
      <!-- Logo à gauche -->
      <div class="flex items-center">
        <img
          src="/images/logo_cnc.png"
          alt="Logo DCOP 413"
          class="h-12 w-auto object-contain"
        />
      </div>

      <!-- Titre centré -->
      <div class="header-center-section">
        <h1 class="header-title">SYSTÈME DE GESTION DES VISITEURS - DCOP (413)</h1>
        <h2 class="header-subtitle inline-block">PREMIÈRE PAGE - AUTHENTIFICATION</h2>
      </div>

    </div>
  </header>

  <!-- Conteneur principal avec header -->
  <div class="modern-form-container-with-header">
    <div class="modern-form-wrapper">

      <!-- Section de connexion -->
      <div class="ultra-form-section">
        <div class="ultra-section-header">
          <h2 class="ultra-section-title">🔑 Authentification</h2>
          <p class="ultra-section-description">Veuillez vous identifier pour accéder à l'application de gestion des visiteurs.</p>
        </div>

        <div class="ultra-form-grid">
          <!-- Formulaire de connexion -->
          <div class="ultra-field-full">
            <form id="loginForm" class="ultra-login-form" novalidate>

              <!-- Champ identifiant -->
              <div class="ultra-field-group">
                <label for="username" class="ultra-label ultra-label-required">Identifiant</label>
                <input
                  type="text"
                  id="username"
                  name="username"
                  required
                  class="ultra-input"
                  placeholder="Votre nom d'utilisateur"
                  autocomplete="username"
                />
              </div>

              <!-- Champ mot de passe -->
              <div class="ultra-field-group">
                <label for="password" class="ultra-label ultra-label-required">Mot de passe</label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  required
                  class="ultra-input"
                  placeholder="Votre mot de passe"
                  autocomplete="current-password"
                />
              </div>

              <!-- Options de connexion -->
              <div class="ultra-login-options">
                <div class="ultra-checkbox-container">
                  <input type="checkbox" id="remember" class="ultra-checkbox-beautiful" />
                  <label for="remember" class="ultra-checkbox-label-beautiful">
                    <div class="ultra-checkbox-icon">✓</div>
                  </label>
                  <span class="ultra-checkbox-text">Se souvenir de moi</span>
                </div>
                <a href="#" class="ultra-forgot-link">Mot de passe oublié ?</a>
              </div>

              <!-- Messages d'erreur et de succès -->
              <div id="errorMessage" class="ultra-message ultra-message-error hidden">
                <div class="ultra-message-icon">❌</div>
                <span id="errorText"></span>
              </div>

              <div id="successMessage" class="ultra-message ultra-message-success hidden">
                <div class="ultra-message-icon">✅</div>
                <span id="successText"></span>
              </div>

              <!-- Bouton de connexion -->
              <div class="ultra-form-actions">
                <button type="submit" id="loginButton" class="ultra-btn ultra-btn-submit ultra-btn-login">
                  🔐 Se connecter
                </button>
              </div>

            </form>
          </div>
        </div>
      </div>

    </div>
  </div>

  <!-- Scripts JavaScript avec vraie authentification -->
  <script>
    // PREMIÈRE PAGE : Script de connexion avec API réelle
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🔐 PREMIÈRE PAGE : Page de connexion chargée');
      console.log('📋 FLUX : 1) Login → 2) Accueil → 3) Formulaire');
      console.log('ℹ️ Page de login accessible sans redirection automatique');

      // Gestion du formulaire de connexion
      const loginForm = document.getElementById('loginForm');
      const submitButton = loginForm?.querySelector('button[type="submit"]');

      if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
          e.preventDefault();

          const username = document.getElementById('username')?.value;
          const password = document.getElementById('password')?.value;

          if (!username || !password) {
            showMessage('Veuillez saisir vos identifiants', 'error');
            return;
          }

          // Désactiver le bouton pendant la connexion
          if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = 'Connexion...';
          }

          try {
            console.log('🔄 Tentative de connexion pour:', username);

            const response = await fetch('/auth/login', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                username: username,
                password: password
              })
            });

            const result = await response.json();

            if (response.ok && result.success) {
              console.log('✅ PREMIÈRE PAGE : Connexion réussie');
              console.log('🔄 Redirection vers DEUXIÈME PAGE : Accueil');
              showMessage('Connexion réussie ! Redirection vers l\'accueil...', 'success');

              // Stockage ultra-simple
              if (result.token) {
                localStorage.setItem('auth_token', result.token);
                console.log('💾 Token stocké');
              }

              // Redirection vers la DEUXIÈME PAGE (Accueil)
              setTimeout(() => {
                window.location.href = '/accueil';
              }, 1000);
            } else {
              console.warn('❌ Échec de connexion:', result.message);
              showMessage(result.message || 'Identifiants invalides', 'error');

              // Afficher info de verrouillage si applicable
              if (result.locked_until) {
                const lockTime = new Date(result.locked_until).toLocaleString();
                showMessage(`Compte verrouillé jusqu'à ${lockTime}`, 'warning');
              }
            }
          } catch (error) {
            console.error('❌ Erreur de connexion:', error);
            showMessage('Erreur de connexion au serveur', 'error');
          } finally {
            // Réactiver le bouton
            if (submitButton) {
              submitButton.disabled = false;
              submitButton.textContent = 'Se connecter';
            }
          }
        });
      }

      // Fonction pour afficher les messages
      function showMessage(message, type = 'info') {
        // Supprimer les anciens messages
        const oldMessages = document.querySelectorAll('.auth-message');
        oldMessages.forEach(msg => msg.remove());

        // Créer le nouveau message
        const messageDiv = document.createElement('div');
        messageDiv.className = `auth-message alert alert-${type}`;
        messageDiv.style.cssText = `
          margin: 10px 0;
          padding: 10px;
          border-radius: 4px;
          font-size: 14px;
        `;

        // Couleurs selon le type
        switch(type) {
          case 'success':
            messageDiv.style.backgroundColor = '#d4edda';
            messageDiv.style.color = '#155724';
            messageDiv.style.border = '1px solid #c3e6cb';
            break;
          case 'error':
            messageDiv.style.backgroundColor = '#f8d7da';
            messageDiv.style.color = '#721c24';
            messageDiv.style.border = '1px solid #f5c6cb';
            break;
          case 'warning':
            messageDiv.style.backgroundColor = '#fff3cd';
            messageDiv.style.color = '#856404';
            messageDiv.style.border = '1px solid #ffeaa7';
            break;
          default:
            messageDiv.style.backgroundColor = '#d1ecf1';
            messageDiv.style.color = '#0c5460';
            messageDiv.style.border = '1px solid #bee5eb';
        }

        messageDiv.textContent = message;

        // Insérer le message avant le formulaire
        loginForm.parentNode.insertBefore(messageDiv, loginForm);

        // Supprimer automatiquement après 5 secondes
        setTimeout(() => {
          if (messageDiv.parentNode) {
            messageDiv.remove();
          }
        }, 5000);
      }
    });
  </script>

</body>
</html>
