<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>DCOP 413 - Système de Gestion des Visiteurs</title>
  <meta name="description" content="Système de gestion des visiteurs - Direction de la Coopération Opérationnelle">
  
  <!-- Scripts de sécurité -->
  <script src="../js_ts/navigation-config.js"></script>
  <script src="../js_ts/auth-guard.js"></script>

  <!-- PREMIÈRE PAGE : Redirection automatique vers la page de connexion -->
  <script>
    // FLUX DE NAVIGATION : index.html → login.html (PREMIÈRE PAGE)
    console.log('🔄 Index.html : Redirection vers la page de connexion (PREMIÈRE PAGE)');

    // Vérification et nettoyage de session avant redirection
    try {
      // Nettoyer toute session existante pour forcer une nouvelle authentification
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_session');
      localStorage.removeItem('dcop413_auth_token');
      localStorage.removeItem('dcop413_user_session');
      sessionStorage.clear();

      // Redirection immédiate et sécurisée vers la page de connexion (PREMIÈRE PAGE)
      console.log('✅ Redirection vers /login (PREMIÈRE PAGE de l\'application)');
      window.location.replace('/login');
    } catch (error) {
      // Fallback en cas d'erreur
      console.warn('⚠️ Fallback redirection vers /login');
      window.location.href = '/login';
    }
  </script>

  <!-- Fallback pour les navigateurs avec JavaScript désactivé -->
  <meta http-equiv="refresh" content="0; url=/login">
  
  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      margin: 0;
      text-align: center;
    }
    
    .loading-container {
      max-width: 400px;
      padding: 2rem;
    }
    
    .logo {
      width: 80px;
      height: 80px;
      margin: 0 auto 1rem;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
    }
    
    .title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }
    
    .subtitle {
      font-size: 1rem;
      opacity: 0.9;
      margin-bottom: 2rem;
    }
    
    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    
    .fallback-link {
      margin-top: 2rem;
      padding: 0.75rem 1.5rem;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      text-decoration: none;
      border-radius: 8px;
      display: inline-block;
      transition: background 0.3s ease;
    }
    
    .fallback-link:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  </style>
</head>

<body>
  <div class="loading-container">
    <div class="logo">🏢</div>
    <h1 class="title">DCOP 413</h1>
    <p class="subtitle">Système de Gestion des Visiteurs</p>
    <div class="loading"></div>
    <p style="margin-top: 1rem; font-size: 0.875rem; opacity: 0.8;">
      Redirection vers la page de connexion (PREMIÈRE PAGE)...
    </p>

    <!-- Lien de secours si la redirection automatique échoue -->
    <noscript>
      <a href="/login" class="fallback-link">
        🔐 Accéder à la page de connexion (PREMIÈRE PAGE)
      </a>
    </noscript>
  </div>

  <script>
    // Vérification supplémentaire après 2 secondes pour s'assurer de la redirection
    setTimeout(() => {
      if (window.location.pathname.includes('index.html') || window.location.pathname === '/') {
        console.log('🔄 Redirection de sécurité vers /login (PREMIÈRE PAGE)');
        window.location.href = '/login';
      }
    }, 2000);
  </script>
</body>
</html>
