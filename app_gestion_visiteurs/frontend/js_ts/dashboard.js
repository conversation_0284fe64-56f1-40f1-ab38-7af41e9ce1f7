/**
 * Tableau de Bord Temps Réel - DCOP 413
 * Statistiques et monitoring avancés
 */

'use strict';

// ===== GESTIONNAIRE DE TABLEAU DE BORD =====
class DashboardManager {
    constructor() {
        this.updateInterval = 30000; // 30 secondes
        this.charts = {};
        this.metrics = {};
        this.isUpdating = false;
        this.init();
    }

    init() {
        console.log('🚀 Initialisation du tableau de bord temps réel');
        this.setupEventListeners();
        this.loadInitialData();
        this.startAutoUpdate();
    }

    setupEventListeners() {
        // Bouton de rafraîchissement manuel
        const refreshBtn = document.getElementById('refreshDashboard');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshData());
        }

        // Sélecteur de période
        const periodSelect = document.getElementById('dashboardPeriod');
        if (periodSelect) {
            periodSelect.addEventListener('change', (e) => {
                this.changePeriod(e.target.value);
            });
        }

        // Mise à jour automatique on/off
        const autoUpdateToggle = document.getElementById('autoUpdateToggle');
        if (autoUpdateToggle) {
            autoUpdateToggle.addEventListener('change', (e) => {
                this.toggleAutoUpdate(e.target.checked);
            });
        }
    }

    async loadInitialData() {
        try {
            await this.fetchMetrics();
            await this.fetchVisitorStats();
            await this.fetchSecurityEvents();
            this.renderDashboard();
        } catch (error) {
            console.error('Erreur lors du chargement initial:', error);
            this.showError('Erreur lors du chargement des données');
        }
    }

    async fetchMetrics() {
        try {
            const response = await fetch('/api/metrics', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('dcop413_auth_token')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }

            this.metrics = await response.json();
            console.log('📊 Métriques mises à jour:', this.metrics);
        } catch (error) {
            console.error('Erreur lors de la récupération des métriques:', error);
            throw error;
        }
    }

    async fetchVisitorStats() {
        try {
            const response = await fetch('/api/dashboard/visitor-stats', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('dcop413_auth_token')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }

            this.visitorStats = await response.json();
            console.log('👥 Statistiques visiteurs mises à jour:', this.visitorStats);
        } catch (error) {
            console.error('Erreur lors de la récupération des stats visiteurs:', error);
            throw error;
        }
    }

    async fetchSecurityEvents() {
        try {
            const response = await fetch('/api/dashboard/security-events', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('dcop413_auth_token')}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`Erreur HTTP: ${response.status}`);
            }

            this.securityEvents = await response.json();
            console.log('🛡️ Événements de sécurité mis à jour:', this.securityEvents);
        } catch (error) {
            console.error('Erreur lors de la récupération des événements de sécurité:', error);
            throw error;
        }
    }

    renderDashboard() {
        this.renderMetricsCards();
        this.renderVisitorStats();
        this.renderSecurityStatus();
        this.renderCharts();
        this.updateLastRefresh();
    }

    renderMetricsCards() {
        // Carte des requêtes totales
        this.updateCard('totalRequests', this.metrics.requests_total || 0, 'Requêtes Totales');
        
        // Carte des sessions actives
        this.updateCard('activeSessions', this.metrics.active_sessions || 0, 'Sessions Actives');
        
        // Carte du temps de réponse moyen
        this.updateCard('avgResponseTime', 
            `${(this.metrics.response_time_avg || 0).toFixed(2)}ms`, 
            'Temps de Réponse Moyen'
        );
        
        // Carte du taux d'erreur
        this.updateCard('errorRate', 
            `${(this.metrics.error_rate || 0).toFixed(2)}%`, 
            'Taux d\'Erreur'
        );
        
        // Carte de l'uptime
        const uptimeHours = Math.floor((this.metrics.uptime_seconds || 0) / 3600);
        this.updateCard('uptime', `${uptimeHours}h`, 'Temps de Fonctionnement');
        
        // Carte des événements de sécurité
        this.updateCard('securityEvents', this.metrics.security_events || 0, 'Événements de Sécurité');
    }

    updateCard(cardId, value, label) {
        const card = document.getElementById(cardId);
        if (card) {
            const valueElement = card.querySelector('.metric-value');
            const labelElement = card.querySelector('.metric-label');
            
            if (valueElement) valueElement.textContent = value;
            if (labelElement) labelElement.textContent = label;
            
            // Animation de mise à jour
            card.classList.add('updated');
            setTimeout(() => card.classList.remove('updated'), 500);
        }
    }

    renderVisitorStats() {
        if (!this.visitorStats) return;

        // Visiteurs du jour
        this.updateCard('visitorsToday', this.visitorStats.today || 0, 'Visiteurs Aujourd\'hui');
        
        // Visites en attente
        this.updateCard('pendingVisits', this.visitorStats.pending || 0, 'Visites en Attente');
        
        // Visites approuvées
        this.updateCard('approvedVisits', this.visitorStats.approved || 0, 'Visites Approuvées');
        
        // Visites en cours
        this.updateCard('ongoingVisits', this.visitorStats.ongoing || 0, 'Visites en Cours');
    }

    renderSecurityStatus() {
        const securityStatus = document.getElementById('securityStatus');
        if (!securityStatus) return;

        const isSecure = (this.metrics.error_rate || 0) < 5 && 
                        (this.metrics.security_events || 0) < 10;

        securityStatus.className = `security-status ${isSecure ? 'secure' : 'warning'}`;
        securityStatus.innerHTML = `
            <div class="security-icon">
                ${isSecure ? '🛡️' : '⚠️'}
            </div>
            <div class="security-text">
                <div class="security-level">${isSecure ? 'SÉCURISÉ' : 'ATTENTION'}</div>
                <div class="security-details">
                    ${isSecure ? 'Tous les systèmes fonctionnent normalement' : 'Activité suspecte détectée'}
                </div>
            </div>
        `;
    }

    renderCharts() {
        // Graphique des visites par heure (simulation)
        this.renderVisitsChart();
        
        // Graphique des événements de sécurité
        this.renderSecurityChart();
        
        // Graphique des performances
        this.renderPerformanceChart();
    }

    renderVisitsChart() {
        const canvas = document.getElementById('visitsChart');
        if (!canvas) return;

        // Simulation de données pour le graphique
        const hours = Array.from({length: 24}, (_, i) => `${i}:00`);
        const visits = Array.from({length: 24}, () => Math.floor(Math.random() * 20));

        // Ici, vous pourriez utiliser Chart.js ou une autre bibliothèque
        // Pour la démo, on affiche juste un indicateur
        const chartContainer = canvas.parentElement;
        chartContainer.innerHTML = `
            <div class="chart-placeholder">
                <h3>Visites par Heure</h3>
                <div class="chart-data">
                    <div class="chart-bar" style="height: 60%;">9h</div>
                    <div class="chart-bar" style="height: 80%;">10h</div>
                    <div class="chart-bar" style="height: 40%;">11h</div>
                    <div class="chart-bar" style="height: 90%;">14h</div>
                    <div class="chart-bar" style="height: 70%;">15h</div>
                </div>
            </div>
        `;
    }

    renderSecurityChart() {
        const canvas = document.getElementById('securityChart');
        if (!canvas) return;

        const chartContainer = canvas.parentElement;
        chartContainer.innerHTML = `
            <div class="chart-placeholder">
                <h3>Événements de Sécurité</h3>
                <div class="security-events-list">
                    <div class="event-item low">Tentative de connexion - Faible</div>
                    <div class="event-item medium">Rate limit dépassé - Moyen</div>
                    <div class="event-item high">Injection SQL bloquée - Élevé</div>
                </div>
            </div>
        `;
    }

    renderPerformanceChart() {
        const canvas = document.getElementById('performanceChart');
        if (!canvas) return;

        const chartContainer = canvas.parentElement;
        chartContainer.innerHTML = `
            <div class="chart-placeholder">
                <h3>Performances Système</h3>
                <div class="performance-metrics">
                    <div class="perf-item">
                        <span class="perf-label">CPU:</span>
                        <div class="perf-bar"><div class="perf-fill" style="width: 45%"></div></div>
                        <span class="perf-value">45%</span>
                    </div>
                    <div class="perf-item">
                        <span class="perf-label">RAM:</span>
                        <div class="perf-bar"><div class="perf-fill" style="width: 62%"></div></div>
                        <span class="perf-value">62%</span>
                    </div>
                    <div class="perf-item">
                        <span class="perf-label">Disque:</span>
                        <div class="perf-bar"><div class="perf-fill" style="width: 28%"></div></div>
                        <span class="perf-value">28%</span>
                    </div>
                </div>
            </div>
        `;
    }

    updateLastRefresh() {
        const lastRefreshElement = document.getElementById('lastRefresh');
        if (lastRefreshElement) {
            const now = new Date();
            lastRefreshElement.textContent = `Dernière mise à jour: ${now.toLocaleTimeString()}`;
        }
    }

    async refreshData() {
        if (this.isUpdating) return;

        this.isUpdating = true;
        const refreshBtn = document.getElementById('refreshDashboard');
        
        try {
            if (refreshBtn) {
                refreshBtn.disabled = true;
                refreshBtn.textContent = 'Mise à jour...';
            }

            await this.loadInitialData();
            
            if (window.notificationManager) {
                window.notificationManager.show('📊 Tableau de bord mis à jour', 'success');
            }
        } catch (error) {
            console.error('Erreur lors de la mise à jour:', error);
            this.showError('Erreur lors de la mise à jour des données');
        } finally {
            this.isUpdating = false;
            if (refreshBtn) {
                refreshBtn.disabled = false;
                refreshBtn.textContent = '🔄 Actualiser';
            }
        }
    }

    startAutoUpdate() {
        this.autoUpdateInterval = setInterval(() => {
            if (!this.isUpdating) {
                this.refreshData();
            }
        }, this.updateInterval);
        
        console.log(`🔄 Mise à jour automatique activée (${this.updateInterval / 1000}s)`);
    }

    stopAutoUpdate() {
        if (this.autoUpdateInterval) {
            clearInterval(this.autoUpdateInterval);
            this.autoUpdateInterval = null;
            console.log('⏹️ Mise à jour automatique désactivée');
        }
    }

    toggleAutoUpdate(enabled) {
        if (enabled) {
            this.startAutoUpdate();
        } else {
            this.stopAutoUpdate();
        }
    }

    changePeriod(period) {
        console.log(`📅 Changement de période: ${period}`);
        // Ici, vous pourriez recharger les données pour la nouvelle période
        this.refreshData();
    }

    showError(message) {
        if (window.notificationManager) {
            window.notificationManager.show(message, 'error');
        } else {
            console.error(message);
        }
    }

    destroy() {
        this.stopAutoUpdate();
        console.log('🗑️ Tableau de bord détruit');
    }
}

// ===== INITIALISATION =====
document.addEventListener('DOMContentLoaded', () => {
    // Vérifier l'authentification
    if (window.AuthManager && !window.AuthManager.requireAuth()) {
        return;
    }

    // Initialiser le tableau de bord seulement sur la page d'accueil
    if (window.location.pathname.includes('accueil.html')) {
        window.dashboardManager = new DashboardManager();
        console.log('📊 Gestionnaire de tableau de bord initialisé');
    }
});

// Nettoyage lors de la fermeture de la page
window.addEventListener('beforeunload', () => {
    if (window.dashboardManager) {
        window.dashboardManager.destroy();
    }
});
