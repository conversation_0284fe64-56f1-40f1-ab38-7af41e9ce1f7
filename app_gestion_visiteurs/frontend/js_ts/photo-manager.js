/**
 * Gestionnaire de Photos pour DCOP 413
 * Gère la sélection, l'affichage et l'impression des photos
 */

class PhotoManager {
  constructor() {
    this.currentPhoto = null;
    this.photoData = null;
    this.maxFileSize = 5 * 1024 * 1024; // 5MB
    this.allowedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    
    this.initializeElements();
    this.bindEvents();
  }

  initializeElements() {
    // Éléments principaux
    this.photoContainer = document.getElementById('headerPhotoContainer');
    this.photoPlaceholder = document.getElementById('photoPlaceholder');
    this.photoPreview = document.getElementById('visitorPhoto');
    this.photoOverlay = document.getElementById('photoOverlay');
    this.photoInfo = document.getElementById('photoInfo');
    
    // Boutons d'action
    this.fileInput = document.getElementById('photoFileInput');
    this.uploadBtn = document.getElementById('uploadPhotoBtn');
    this.captureBtn = document.getElementById('capturePhotoBtn');
    this.removeBtn = document.getElementById('removePhotoBtn');
    
    // Boutons overlay
    this.changeBtn = document.getElementById('changePhotoBtn');
    this.printBtn = document.getElementById('printPhotoBtn');
    this.removeOverlayBtn = document.getElementById('removePhotoOverlayBtn');
    
    // Éléments d'information
    this.photoSize = document.getElementById('photoSize');
    this.photoFormat = document.getElementById('photoFormat');
  }

  bindEvents() {
    // Événements de sélection de fichier
    this.fileInput?.addEventListener('change', (e) => this.handleFileSelect(e));
    this.uploadBtn?.addEventListener('click', () => this.openFileDialog());
    this.photoContainer?.addEventListener('click', () => this.handleContainerClick());
    
    // Événements des boutons d'action
    this.captureBtn?.addEventListener('click', () => this.openCamera());
    this.removeBtn?.addEventListener('click', () => this.removePhoto());
    
    // Événements overlay
    this.changeBtn?.addEventListener('click', (e) => {
      e.stopPropagation();
      this.openFileDialog();
    });
    this.printBtn?.addEventListener('click', (e) => {
      e.stopPropagation();
      this.printWithPhoto();
    });
    this.removeOverlayBtn?.addEventListener('click', (e) => {
      e.stopPropagation();
      this.removePhoto();
    });

    // Drag & Drop
    this.photoContainer?.addEventListener('dragover', (e) => this.handleDragOver(e));
    this.photoContainer?.addEventListener('drop', (e) => this.handleDrop(e));
    this.photoContainer?.addEventListener('dragenter', (e) => this.handleDragEnter(e));
    this.photoContainer?.addEventListener('dragleave', (e) => this.handleDragLeave(e));
  }

  handleContainerClick() {
    if (!this.currentPhoto) {
      this.openFileDialog();
    }
  }

  openFileDialog() {
    this.fileInput?.click();
  }

  handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
      this.processFile(file);
    }
  }

  processFile(file) {
    // Validation du fichier
    if (!this.validateFile(file)) {
      return;
    }

    // Lecture du fichier
    const reader = new FileReader();
    reader.onload = (e) => {
      this.displayPhoto(e.target.result, file);
    };
    reader.onerror = () => {
      this.showError('Erreur lors de la lecture du fichier');
    };
    reader.readAsDataURL(file);
  }

  validateFile(file) {
    // Vérification du format
    if (!this.allowedFormats.includes(file.type)) {
      this.showError(`Format non supporté. Formats acceptés: ${this.allowedFormats.join(', ')}`);
      return false;
    }

    // Vérification de la taille
    if (file.size > this.maxFileSize) {
      this.showError(`Fichier trop volumineux. Taille maximale: ${this.maxFileSize / (1024 * 1024)}MB`);
      return false;
    }

    return true;
  }

  displayPhoto(photoData, file) {
    this.currentPhoto = photoData;
    this.photoData = file;

    // Affichage de la photo
    if (this.photoPreview) {
      this.photoPreview.src = photoData;
      this.photoPreview.style.display = 'block';
    }

    // Masquage du placeholder
    if (this.photoPlaceholder) {
      this.photoPlaceholder.style.display = 'none';
    }

    // Affichage de l'overlay
    if (this.photoOverlay) {
      this.photoOverlay.style.display = 'flex';
    }

    // Affichage des informations
    this.updatePhotoInfo(file);

    // Affichage du bouton supprimer
    if (this.removeBtn) {
      this.removeBtn.style.display = 'flex';
    }

    // Animation de succès
    this.animateSuccess();

    console.log('✅ Photo chargée avec succès:', file.name);
  }

  updatePhotoInfo(file) {
    if (this.photoSize) {
      const sizeKB = Math.round(file.size / 1024);
      this.photoSize.textContent = `${sizeKB}KB`;
    }

    if (this.photoFormat) {
      this.photoFormat.textContent = file.type.split('/')[1].toUpperCase();
    }

    if (this.photoInfo) {
      this.photoInfo.style.display = 'flex';
    }
  }

  removePhoto() {
    this.currentPhoto = null;
    this.photoData = null;

    // Masquage de la photo
    if (this.photoPreview) {
      this.photoPreview.style.display = 'none';
      this.photoPreview.src = '';
    }

    // Affichage du placeholder
    if (this.photoPlaceholder) {
      this.photoPlaceholder.style.display = 'flex';
    }

    // Masquage de l'overlay
    if (this.photoOverlay) {
      this.photoOverlay.style.display = 'none';
    }

    // Masquage des informations
    if (this.photoInfo) {
      this.photoInfo.style.display = 'none';
    }

    // Masquage du bouton supprimer
    if (this.removeBtn) {
      this.removeBtn.style.display = 'none';
    }

    // Reset de l'input file
    if (this.fileInput) {
      this.fileInput.value = '';
    }

    console.log('🗑️ Photo supprimée');
  }

  // Drag & Drop handlers
  handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    this.photoContainer?.classList.add('drag-over');
  }

  handleDragEnter(e) {
    e.preventDefault();
    e.stopPropagation();
  }

  handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    this.photoContainer?.classList.remove('drag-over');
  }

  handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    this.photoContainer?.classList.remove('drag-over');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      this.processFile(files[0]);
    }
  }

  openCamera() {
    // Intégration avec le système de caméra existant
    if (window.cameraManager) {
      window.cameraManager.openCamera();
    } else {
      console.log('📸 Ouverture de la caméra...');
      // Fallback ou implémentation de base
      this.showInfo('Fonctionnalité caméra en cours de développement');
    }
  }

  printWithPhoto() {
    if (!this.currentPhoto) {
      this.showError('Aucune photo à imprimer');
      return;
    }

    console.log('🖨️ Impression avec photo...');
    
    // Préparation de l'impression avec la photo
    this.prepareForPrint();
    
    // Lancement de l'impression
    setTimeout(() => {
      window.print();
    }, 500);
  }

  prepareForPrint() {
    // Ajout de la photo dans la version imprimable
    const printPhotoContainer = document.querySelector('.ultra-print-photo');
    if (printPhotoContainer && this.currentPhoto) {
      printPhotoContainer.innerHTML = `<img src="${this.currentPhoto}" alt="Photo du visiteur" style="width: 100%; height: 100%; object-fit: cover;" />`;
    }
  }

  animateSuccess() {
    this.photoContainer?.classList.add('photo-success');
    setTimeout(() => {
      this.photoContainer?.classList.remove('photo-success');
    }, 1000);
  }

  showError(message) {
    console.error('❌ Erreur photo:', message);
    // Ici vous pouvez ajouter une notification toast ou modal
    alert(`Erreur: ${message}`);
  }

  showInfo(message) {
    console.log('ℹ️ Info photo:', message);
    // Ici vous pouvez ajouter une notification toast ou modal
    alert(`Info: ${message}`);
  }

  // Méthodes publiques pour l'intégration
  getPhotoData() {
    return this.currentPhoto;
  }

  hasPhoto() {
    return !!this.currentPhoto;
  }

  setPhoto(photoData, filename = 'photo.jpg') {
    // Méthode pour définir une photo programmatiquement
    const mockFile = {
      name: filename,
      size: 0,
      type: 'image/jpeg'
    };
    this.displayPhoto(photoData, mockFile);
  }
}

// Initialisation automatique
document.addEventListener('DOMContentLoaded', () => {
  window.photoManager = new PhotoManager();
  console.log('📷 PhotoManager initialisé');
});

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PhotoManager;
}
