/**
 * Gestionnaire d'Impression pour DCOP 413
 * Gère l'impression optimisée avec photo et données du formulaire
 */

class PrintManager {
  constructor() {
    this.formData = {};
    this.photoData = null;
    this.initializeElements();
  }

  initializeElements() {
    // Éléments du formulaire
    this.form = document.getElementById('visitorForm');
    
    // Éléments d'impression
    this.printSection = document.getElementById('printSection');
    this.printPhotoContainer = document.getElementById('printPhotoContainer');
    this.printGenerationDate = document.getElementById('printGenerationDate');
    
    // Éléments de données d'impression
    this.printElements = {
      nom: document.getElementById('printNom'),
      telephone: document.getElementById('printTelephone'),
      email: document.getElementById('printEmail'),
      entreprise: document.getElementById('printEntreprise'),
      personneVoir: document.getElementById('printPersonneVoir'),
      motif: document.getElementById('printMotif'),
      dateVisite: document.getElementById('printDateVisite'),
      heureArrivee: document.getElementById('printHeureArrivee')
    };
  }

  collectFormData() {
    if (!this.form) {
      console.error('Formulaire non trouvé');
      return false;
    }

    // Collecte des données du formulaire
    const formData = new FormData(this.form);
    this.formData = {};

    // Extraction des données principales
    this.formData.nom = formData.get('nom') || '';
    this.formData.prenom = formData.get('prenom') || '';
    this.formData.telephone = formData.get('telephone') || '';
    this.formData.email = formData.get('email') || '';
    this.formData.entreprise = formData.get('entreprise') || '';
    this.formData.personne_a_voir = formData.get('personne_a_voir') || '';
    this.formData.motif_visite = formData.get('motif_visite') || '';
    this.formData.date_visite = formData.get('date_visite') || '';
    this.formData.heure_arrivee = formData.get('heure_arrivee') || '';

    // Récupération de la photo si disponible
    if (window.photoManager && window.photoManager.hasPhoto()) {
      this.photoData = window.photoManager.getPhotoData();
    }

    return true;
  }

  populatePrintData() {
    // Remplissage des éléments d'impression
    if (this.printElements.nom) {
      this.printElements.nom.textContent = `${this.formData.prenom} ${this.formData.nom}`.trim();
    }
    
    if (this.printElements.telephone) {
      this.printElements.telephone.textContent = this.formData.telephone;
    }
    
    if (this.printElements.email) {
      this.printElements.email.textContent = this.formData.email;
    }
    
    if (this.printElements.entreprise) {
      this.printElements.entreprise.textContent = this.formData.entreprise;
    }
    
    if (this.printElements.personneVoir) {
      this.printElements.personneVoir.textContent = this.formData.personne_a_voir;
    }
    
    if (this.printElements.motif) {
      this.printElements.motif.textContent = this.formData.motif_visite;
    }
    
    if (this.printElements.dateVisite) {
      this.printElements.dateVisite.textContent = this.formatDate(this.formData.date_visite);
    }
    
    if (this.printElements.heureArrivee) {
      this.printElements.heureArrivee.textContent = this.formData.heure_arrivee;
    }

    // Ajout de la photo
    this.addPhotoToPrint();

    // Date de génération
    if (this.printGenerationDate) {
      this.printGenerationDate.textContent = this.formatDateTime(new Date());
    }
  }

  addPhotoToPrint() {
    if (!this.printPhotoContainer) return;

    if (this.photoData) {
      this.printPhotoContainer.innerHTML = `
        <img src="${this.photoData}" alt="Photo du visiteur" style="width: 100%; height: 100%; object-fit: cover;" />
      `;
    } else {
      this.printPhotoContainer.innerHTML = `
        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; color: #666; font-size: 10pt;">
          <div style="font-size: 24pt; margin-bottom: 8px;">📷</div>
          <div>Aucune photo</div>
        </div>
      `;
    }
  }

  formatDate(dateString) {
    if (!dateString) return '';
    
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('fr-FR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      return dateString;
    }
  }

  formatDateTime(date) {
    const options = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };

    return date.toLocaleDateString('fr-FR', options).replace(' à ', ' à ');
  }

  validateFormData() {
    const requiredFields = ['nom', 'prenom', 'telephone'];
    const missingFields = [];

    for (const field of requiredFields) {
      if (!this.formData[field] || this.formData[field].trim() === '') {
        missingFields.push(field);
      }
    }

    if (missingFields.length > 0) {
      this.showError(`Veuillez remplir les champs obligatoires: ${missingFields.join(', ')}`);
      return false;
    }

    return true;
  }

  async printDocument() {
    try {
      // Collecte des données
      if (!this.collectFormData()) {
        return false;
      }

      // Validation
      if (!this.validateFormData()) {
        return false;
      }

      // Remplissage des données d'impression
      this.populatePrintData();

      // Préparation de l'impression
      this.prepareForPrint();

      // Attendre un peu pour que les données soient bien chargées
      await this.delay(300);

      // Lancement de l'impression
      window.print();

      // Nettoyage après impression
      this.cleanupAfterPrint();

      return true;

    } catch (error) {
      console.error('Erreur lors de l\'impression:', error);
      this.showError('Erreur lors de la préparation de l\'impression');
      return false;
    }
  }

  prepareForPrint() {
    // Ajout de classes pour l'impression
    document.body.classList.add('printing');
    
    // Optimisation des images pour l'impression
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      img.style.maxWidth = '100%';
      img.style.height = 'auto';
    });

    console.log('📄 Document préparé pour l\'impression');
  }

  cleanupAfterPrint() {
    // Nettoyage après impression
    setTimeout(() => {
      document.body.classList.remove('printing');
      console.log('🧹 Nettoyage post-impression effectué');
    }, 1000);
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  showError(message) {
    console.error('❌ Erreur impression:', message);
    alert(`Erreur d'impression: ${message}`);
  }

  showSuccess(message) {
    console.log('✅ Succès impression:', message);
  }

  // Méthodes publiques
  async print() {
    return await this.printDocument();
  }

  setPhotoData(photoData) {
    this.photoData = photoData;
  }

  getFormData() {
    this.collectFormData();
    return this.formData;
  }
}

// Fonction globale pour l'impression
async function printFormWithPhoto() {
  if (!window.printManager) {
    window.printManager = new PrintManager();
  }

  const success = await window.printManager.print();
  
  if (success) {
    console.log('🖨️ Impression lancée avec succès');
  } else {
    console.error('❌ Échec de l\'impression');
  }
}

// Initialisation automatique
document.addEventListener('DOMContentLoaded', () => {
  window.printManager = new PrintManager();
  console.log('🖨️ PrintManager initialisé');

  // Écoute des événements d'impression
  window.addEventListener('beforeprint', () => {
    console.log('📄 Début d\'impression...');
  });

  window.addEventListener('afterprint', () => {
    console.log('📄 Fin d\'impression');
  });
});

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PrintManager;
}
