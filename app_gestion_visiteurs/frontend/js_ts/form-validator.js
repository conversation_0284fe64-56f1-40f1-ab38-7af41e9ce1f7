/**
 * Validateur de Formulaire pour DCOP 413
 * Gère la validation en temps réel et l'optimisation UX
 */

class FormValidator {
  constructor() {
    this.form = null;
    this.fields = {};
    this.validationRules = {};
    this.isValid = false;
    
    this.initializeValidator();
  }

  initializeValidator() {
    this.form = document.getElementById('visitorForm');
    if (!this.form) {
      console.warn('Formulaire non trouvé');
      return;
    }

    this.setupValidationRules();
    this.bindEvents();
    this.initializeFields();
    
    console.log('✅ FormValidator initialisé');
  }

  setupValidationRules() {
    this.validationRules = {
      nom: {
        required: true,
        minLength: 2,
        maxLength: 50,
        pattern: /^[A-Za-zÀ-ÿ\s\-']{2,50}$/,
        message: 'Le nom doit contenir 2-50 caractères (lettres uniquement)'
      },
      prenom: {
        required: true,
        minLength: 2,
        maxLength: 50,
        pattern: /^[A-Za-zÀ-ÿ\s\-']{2,50}$/,
        message: 'Le prénom doit contenir 2-50 caractères (lettres uniquement)'
      },
      postnom: {
        required: false,
        minLength: 2,
        maxLength: 50,
        pattern: /^[A-Za-zÀ-ÿ\s\-']{2,50}$/,
        message: 'Le post-nom doit contenir 2-50 caractères (lettres uniquement)'
      },
      telephone: {
        required: true,
        pattern: /^(\+243|0)?[0-9]{9,10}$/,
        message: 'Numéro de téléphone invalide (format: +243XXXXXXXXX ou 0XXXXXXXXX)'
      },
      email: {
        required: false,
        pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: 'Adresse email invalide'
      },
      entreprise: {
        required: false,
        maxLength: 100,
        message: 'Le nom de l\'entreprise ne peut dépasser 100 caractères'
      },
      personne_a_voir: {
        required: true,
        minLength: 2,
        maxLength: 100,
        message: 'Veuillez indiquer la personne à voir (2-100 caractères)'
      },
      motif_visite: {
        required: true,
        minLength: 5,
        maxLength: 500,
        message: 'Le motif de visite doit contenir 5-500 caractères'
      },
      date_visite: {
        required: true,
        futureDate: true,
        message: 'Veuillez sélectionner une date de visite valide'
      },
      heure_arrivee: {
        required: true,
        pattern: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
        message: 'Veuillez indiquer une heure valide (HH:MM)'
      }
    };
  }

  initializeFields() {
    Object.keys(this.validationRules).forEach(fieldName => {
      const field = document.getElementById(fieldName);
      if (field) {
        this.fields[fieldName] = {
          element: field,
          isValid: false,
          errorElement: this.createErrorElement(fieldName),
          successElement: this.createSuccessElement(fieldName)
        };
        
        // Ajouter les éléments d'aide après le champ
        const parent = field.parentNode;
        parent.appendChild(this.fields[fieldName].errorElement);
        parent.appendChild(this.fields[fieldName].successElement);
      }
    });
  }

  createErrorElement(fieldName) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error hidden';
    errorDiv.id = `${fieldName}-error`;
    errorDiv.innerHTML = '<span>⚠️</span><span class="error-text"></span>';
    return errorDiv;
  }

  createSuccessElement(fieldName) {
    const successDiv = document.createElement('div');
    successDiv.className = 'field-success hidden';
    successDiv.id = `${fieldName}-success`;
    successDiv.innerHTML = '<span>✅</span><span>Valide</span>';
    return successDiv;
  }

  bindEvents() {
    Object.keys(this.fields).forEach(fieldName => {
      const field = this.fields[fieldName]?.element;
      if (field) {
        // Validation en temps réel
        field.addEventListener('input', () => this.validateField(fieldName));
        field.addEventListener('blur', () => this.validateField(fieldName));
        field.addEventListener('focus', () => this.clearFieldStatus(fieldName));
      }
    });

    // Validation du formulaire complet
    this.form.addEventListener('submit', (e) => this.handleSubmit(e));
  }

  validateField(fieldName) {
    const fieldData = this.fields[fieldName];
    const rules = this.validationRules[fieldName];
    
    if (!fieldData || !rules) return false;

    const value = fieldData.element.value.trim();
    const errors = [];

    // Validation required
    if (rules.required && !value) {
      errors.push('Ce champ est obligatoire');
    }

    // Validation si le champ a une valeur
    if (value) {
      // Validation longueur minimale
      if (rules.minLength && value.length < rules.minLength) {
        errors.push(`Minimum ${rules.minLength} caractères`);
      }

      // Validation longueur maximale
      if (rules.maxLength && value.length > rules.maxLength) {
        errors.push(`Maximum ${rules.maxLength} caractères`);
      }

      // Validation pattern
      if (rules.pattern && !rules.pattern.test(value)) {
        errors.push(rules.message);
      }

      // Validation date future
      if (rules.futureDate) {
        const selectedDate = new Date(value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (selectedDate < today) {
          errors.push('La date ne peut pas être dans le passé');
        }
      }
    }

    // Mise à jour du statut du champ
    fieldData.isValid = errors.length === 0;
    this.updateFieldStatus(fieldName, errors);

    return fieldData.isValid;
  }

  updateFieldStatus(fieldName, errors) {
    const fieldData = this.fields[fieldName];
    if (!fieldData) return;

    const { element, errorElement, successElement } = fieldData;

    // Reset des classes
    element.classList.remove('error', 'success');
    errorElement.classList.add('hidden');
    successElement.classList.add('hidden');

    if (errors.length > 0) {
      // Affichage des erreurs
      element.classList.add('error');
      errorElement.querySelector('.error-text').textContent = errors[0];
      errorElement.classList.remove('hidden');
    } else if (element.value.trim()) {
      // Affichage du succès
      element.classList.add('success');
      successElement.classList.remove('hidden');
    }
  }

  clearFieldStatus(fieldName) {
    const fieldData = this.fields[fieldName];
    if (!fieldData) return;

    const { element, errorElement, successElement } = fieldData;
    
    element.classList.remove('error');
    errorElement.classList.add('hidden');
  }

  validateForm() {
    let isFormValid = true;
    const errors = [];

    Object.keys(this.validationRules).forEach(fieldName => {
      const isFieldValid = this.validateField(fieldName);
      if (!isFieldValid) {
        isFormValid = false;
        errors.push(fieldName);
      }
    });

    this.isValid = isFormValid;
    return { isValid: isFormValid, errors };
  }

  handleSubmit(event) {
    event.preventDefault();
    
    const validation = this.validateForm();
    
    if (!validation.isValid) {
      this.showFormErrors(validation.errors);
      this.focusFirstError();
      return false;
    }

    // Validation de la photo (optionnelle mais recommandée)
    if (window.photoManager && !window.photoManager.hasPhoto()) {
      const confirmWithoutPhoto = confirm(
        'Aucune photo n\'a été ajoutée. Voulez-vous continuer sans photo ?'
      );
      if (!confirmWithoutPhoto) {
        return false;
      }
    }

    this.showSuccess('Formulaire valide ! Envoi en cours...');
    this.submitForm();
    
    return true;
  }

  submitForm() {
    // Ici vous pouvez ajouter la logique d'envoi du formulaire
    console.log('📤 Envoi du formulaire...');
    
    // Simulation d'envoi
    setTimeout(() => {
      this.showSuccess('✅ Visiteur enregistré avec succès !');
    }, 1000);
  }

  focusFirstError() {
    const firstErrorField = Object.keys(this.fields).find(fieldName => 
      !this.fields[fieldName].isValid
    );
    
    if (firstErrorField && this.fields[firstErrorField]) {
      this.fields[firstErrorField].element.focus();
      this.fields[firstErrorField].element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center' 
      });
    }
  }

  showFormErrors(errors) {
    console.error('❌ Erreurs de validation:', errors);
    // Ici vous pouvez ajouter une notification toast ou modal
  }

  showSuccess(message) {
    console.log('✅ Succès:', message);
    // Ici vous pouvez ajouter une notification toast ou modal
  }

  // Méthodes publiques
  getFormData() {
    const formData = new FormData(this.form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
      data[key] = value;
    }
    
    return data;
  }

  isFormValid() {
    return this.validateForm().isValid;
  }

  resetForm() {
    this.form.reset();
    Object.keys(this.fields).forEach(fieldName => {
      this.clearFieldStatus(fieldName);
      this.fields[fieldName].isValid = false;
    });
  }
}

// Initialisation automatique
document.addEventListener('DOMContentLoaded', () => {
  window.formValidator = new FormValidator();
});

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = FormValidator;
}
