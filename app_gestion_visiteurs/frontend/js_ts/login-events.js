/**
 * Gestionnaire d'événements pour la page de connexion
 * Séparation propre du JavaScript selon les bonnes pratiques
 */

'use strict';

// ===== GESTIONNAIRE D'ÉVÉNEMENTS LOGIN =====
class LoginEventManager {
  constructor() {
    this.init();
  }

  init() {
    this.bindEvents();
    this.checkExistingAuth();
  }

  bindEvents() {
    // Événements pour les liens de développement
    this.bindDevelopmentLinks();
    
    // Événements pour le formulaire de connexion
    this.bindLoginForm();
  }

  bindDevelopmentLinks() {
    const devLinks = document.querySelectorAll('[data-dev-feature]');
    devLinks.forEach(link => {
      link.addEventListener('click', this.handleDevelopmentFeature.bind(this));
    });
  }

  bindLoginForm() {
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
      loginForm.addEventListener('submit', this.handleLoginSubmit.bind(this));
    }
  }

  handleDevelopmentFeature(event) {
    event.preventDefault();
    
    const featureName = event.currentTarget.getAttribute('data-dev-feature');
    this.showDevelopmentAlert(featureName);
  }

  async handleLoginSubmit(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const username = formData.get('username')?.trim();
    const password = formData.get('password')?.trim();
    
    // Validation côté client
    if (!this.validateLoginForm(username, password)) {
      return;
    }
    
    // Désactiver le formulaire pendant la connexion
    this.setFormLoading(true);
    
    try {
      // Utiliser AuthManager pour la connexion
      if (window.AuthManager) {
        const success = await window.AuthManager.login(username, password);
        if (success) {
          this.showSuccessMessage('Connexion réussie ! Redirection...');
        } else {
          throw new Error('Échec de la connexion');
        }
      } else {
        // Fallback si AuthManager n'est pas disponible
        await this.fallbackLogin(username, password);
      }
    } catch (error) {
      console.error('Erreur de connexion:', error);
      this.showErrorMessage('Identifiants incorrects. Veuillez réessayer.');
    } finally {
      this.setFormLoading(false);
    }
  }

  validateLoginForm(username, password) {
    this.hideMessages();
    
    if (!username || !password) {
      this.showErrorMessage('Veuillez remplir tous les champs obligatoires');
      return false;
    }
    
    if (username.length < 2) {
      this.showErrorMessage('Le nom d\'utilisateur doit contenir au moins 2 caractères');
      return false;
    }
    
    if (password.length < 3) {
      this.showErrorMessage('Le mot de passe doit contenir au moins 3 caractères');
      return false;
    }
    
    return true;
  }

  async fallbackLogin(username, password) {
    // Simulation de connexion pour fallback
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Stocker les données de session
    const sessionData = {
      token: "fallback_token_" + Date.now(),
      username: username,
      loginTime: new Date().toISOString(),
      isAuthenticated: true
    };
    
    localStorage.setItem("auth_token", sessionData.token);
    localStorage.setItem("user_session", JSON.stringify(sessionData));
    
    this.showSuccessMessage('Connexion réussie ! Redirection...');
    
    setTimeout(() => {
      window.location.href = '/accueil';
    }, 1500);
  }

  setFormLoading(isLoading) {
    const loginButton = document.getElementById('loginButton');
    const form = document.getElementById('loginForm');
    
    if (loginButton) {
      loginButton.disabled = isLoading;
      loginButton.textContent = isLoading ? '🔄 Connexion...' : '🔐 Se connecter';
    }
    
    if (form) {
      const inputs = form.querySelectorAll('input');
      inputs.forEach(input => {
        input.disabled = isLoading;
      });
    }
  }

  showErrorMessage(message) {
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    
    if (errorMessage && errorText) {
      errorText.textContent = message;
      errorMessage.classList.remove('hidden');
    } else {
      // Fallback
      if (window.notificationManager) {
        window.notificationManager.show(message, 'error');
      } else {
        alert(message);
      }
    }
  }

  showSuccessMessage(message) {
    const successMessage = document.getElementById('successMessage');
    const successText = document.getElementById('successText');
    
    if (successMessage && successText) {
      successText.textContent = message;
      successMessage.classList.remove('hidden');
    } else {
      // Fallback
      if (window.notificationManager) {
        window.notificationManager.show(message, 'success');
      } else {
        alert(message);
      }
    }
  }

  hideMessages() {
    const errorMessage = document.getElementById('errorMessage');
    const successMessage = document.getElementById('successMessage');
    
    if (errorMessage) {
      errorMessage.classList.add('hidden');
    }
    
    if (successMessage) {
      successMessage.classList.add('hidden');
    }
  }

  showDevelopmentAlert(featureName) {
    let message;
    
    switch (featureName) {
      case 'Aide':
        message = 'Fonctionnalité en développement - Contactez l\'administrateur';
        break;
      default:
        message = `Fonctionnalité en développement : ${featureName}`;
    }
    
    if (window.notificationManager) {
      window.notificationManager.show(`🚧 ${featureName} en développement`, 'warning');
    } else {
      alert(`${message}\n\nCette fonctionnalité sera disponible dans une prochaine version.`);
    }
  }

  checkExistingAuth() {
    // Vérifier si l'utilisateur est déjà connecté
    if (window.AuthManager && window.AuthManager.isAuthenticated()) {
      console.log('🔄 Utilisateur déjà connecté, redirection vers accueil');
      if (window.AuthManager.redirectToHome) {
        window.AuthManager.redirectToHome();
      } else {
        window.location.href = '/accueil';
      }
    }
  }
}

// ===== FONCTIONS GLOBALES POUR COMPATIBILITÉ =====

window.handleForgotPassword = function(event) {
  event.preventDefault();
  
  if (window.loginEventManager) {
    window.loginEventManager.showDevelopmentAlert('Mot de passe oublié');
  } else {
    alert('Fonctionnalité en développement - Contactez l\'administrateur\n\nCette fonctionnalité sera disponible dans une prochaine version.');
  }
};

window.handleDevelopmentAlert = function(featureName) {
  if (window.loginEventManager) {
    window.loginEventManager.showDevelopmentAlert(featureName);
  } else {
    alert(`Fonctionnalité en développement : ${featureName}\n\nCette fonctionnalité sera disponible dans une prochaine version.`);
  }
};

// ===== INITIALISATION =====
document.addEventListener('DOMContentLoaded', () => {
  // Initialiser le gestionnaire d'événements
  window.loginEventManager = new LoginEventManager();
  console.log('🔐 Gestionnaire d\'événements login initialisé');

  // Vérifier si l'utilisateur est déjà connecté (éviter les boucles)
  setTimeout(() => {
    window.loginEventManager.checkExistingAuth();
  }, 500);
});

// Export pour utilisation dans d'autres modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = LoginEventManager;
}
