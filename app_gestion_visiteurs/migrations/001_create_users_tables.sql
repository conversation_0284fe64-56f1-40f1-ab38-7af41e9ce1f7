-- Migration 001: Création des tables d'utilisateurs sécurisées
-- Système d'authentification ultra-sécurisé pour DCOP 413

-- Extension pour UUID
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Table des utilisateurs sécurisés
CREATE TABLE IF NOT EXISTS secure_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'user',
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_login TIMESTAMPTZ,
    failed_login_attempts INTEGER NOT NULL DEFAULT 0,
    account_locked_until TIMESTAMPTZ,
    password_changed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_by U<PERSON><PERSON>,
    updated_by UUID,
    
    -- Contraintes de sécurité
    CONSTRAINT chk_username_length CHECK (LENGTH(username) >= 3),
    CONSTRAINT chk_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT chk_role_valid CHECK (role IN ('admin', 'operator', 'user', 'readonly')),
    CONSTRAINT chk_failed_attempts CHECK (failed_login_attempts >= 0),
    
    -- Clés étrangères
    FOREIGN KEY (created_by) REFERENCES secure_users(id),
    FOREIGN KEY (updated_by) REFERENCES secure_users(id)
);

-- Table d'audit des connexions
CREATE TABLE IF NOT EXISTS login_audit (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    ip_address INET NOT NULL,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    failure_reason TEXT,
    attempted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Clé étrangère
    FOREIGN KEY (user_id) REFERENCES secure_users(id) ON DELETE CASCADE
);

-- Table des sessions sécurisées
CREATE TABLE IF NOT EXISTS secure_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    session_token_hash TEXT NOT NULL,
    jwt_token_id TEXT NOT NULL, -- JTI du JWT
    ip_address INET NOT NULL,
    user_agent TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    last_activity TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    is_active BOOLEAN NOT NULL DEFAULT true,
    revoked_at TIMESTAMPTZ,
    revoked_reason TEXT,
    
    -- Contraintes
    CONSTRAINT chk_expires_after_created CHECK (expires_at > created_at),
    CONSTRAINT chk_last_activity_valid CHECK (last_activity >= created_at),
    
    -- Clé étrangère
    FOREIGN KEY (user_id) REFERENCES secure_users(id) ON DELETE CASCADE
);

-- Table des tokens révoqués (blacklist JWT)
CREATE TABLE IF NOT EXISTS revoked_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    jti TEXT UNIQUE NOT NULL, -- JWT ID
    user_id UUID NOT NULL,
    revoked_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    revoked_by UUID,
    reason TEXT,
    expires_at TIMESTAMPTZ NOT NULL, -- Quand le token aurait expiré naturellement
    
    -- Clé étrangère
    FOREIGN KEY (user_id) REFERENCES secure_users(id) ON DELETE CASCADE,
    FOREIGN KEY (revoked_by) REFERENCES secure_users(id)
);

-- Index pour les performances et la sécurité
CREATE INDEX IF NOT EXISTS idx_secure_users_username ON secure_users(username);
CREATE INDEX IF NOT EXISTS idx_secure_users_email ON secure_users(email);
CREATE INDEX IF NOT EXISTS idx_secure_users_active ON secure_users(is_active);
CREATE INDEX IF NOT EXISTS idx_secure_users_locked ON secure_users(account_locked_until) WHERE account_locked_until IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_login_audit_user_id ON login_audit(user_id);
CREATE INDEX IF NOT EXISTS idx_login_audit_ip ON login_audit(ip_address);
CREATE INDEX IF NOT EXISTS idx_login_audit_attempted_at ON login_audit(attempted_at);
CREATE INDEX IF NOT EXISTS idx_login_audit_success ON login_audit(success);

CREATE INDEX IF NOT EXISTS idx_secure_sessions_user_id ON secure_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_secure_sessions_token_hash ON secure_sessions(session_token_hash);
CREATE INDEX IF NOT EXISTS idx_secure_sessions_jti ON secure_sessions(jwt_token_id);
CREATE INDEX IF NOT EXISTS idx_secure_sessions_active ON secure_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_secure_sessions_expires ON secure_sessions(expires_at);

CREATE INDEX IF NOT EXISTS idx_revoked_tokens_jti ON revoked_tokens(jti);
CREATE INDEX IF NOT EXISTS idx_revoked_tokens_user_id ON revoked_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_revoked_tokens_expires ON revoked_tokens(expires_at);

-- Fonction pour mettre à jour automatiquement updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger pour updated_at sur secure_users
CREATE TRIGGER update_secure_users_updated_at 
    BEFORE UPDATE ON secure_users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Fonction de nettoyage automatique des sessions expirées
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM secure_sessions 
    WHERE expires_at < NOW() OR (is_active = false AND revoked_at < NOW() - INTERVAL '7 days');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Nettoyer aussi les tokens révoqués expirés
    DELETE FROM revoked_tokens 
    WHERE expires_at < NOW() - INTERVAL '7 days';
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Fonction de sécurité pour détecter les tentatives de brute force
CREATE OR REPLACE FUNCTION detect_brute_force_attempts(
    p_ip_address INET,
    p_time_window INTERVAL DEFAULT '15 minutes',
    p_max_attempts INTEGER DEFAULT 10
)
RETURNS BOOLEAN AS $$
DECLARE
    attempt_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO attempt_count
    FROM login_audit
    WHERE ip_address = p_ip_address
      AND success = false
      AND attempted_at > NOW() - p_time_window;
    
    RETURN attempt_count >= p_max_attempts;
END;
$$ LANGUAGE plpgsql;

-- Vue pour les statistiques de sécurité
CREATE OR REPLACE VIEW security_stats AS
SELECT 
    DATE(attempted_at) as date,
    COUNT(*) as total_attempts,
    COUNT(*) FILTER (WHERE success = true) as successful_logins,
    COUNT(*) FILTER (WHERE success = false) as failed_attempts,
    COUNT(DISTINCT ip_address) as unique_ips,
    COUNT(DISTINCT user_id) as unique_users
FROM login_audit
WHERE attempted_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(attempted_at)
ORDER BY date DESC;

-- Politique de sécurité Row Level Security (RLS)
ALTER TABLE secure_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE login_audit ENABLE ROW LEVEL SECURITY;
ALTER TABLE secure_sessions ENABLE ROW LEVEL SECURITY;

-- Politique pour les utilisateurs normaux (peuvent voir seulement leurs propres données)
CREATE POLICY user_own_data ON secure_users
    FOR ALL
    TO authenticated_user
    USING (id = current_setting('app.current_user_id')::UUID);

-- Politique pour les administrateurs (peuvent voir toutes les données)
CREATE POLICY admin_all_data ON secure_users
    FOR ALL
    TO admin_user
    USING (true);

-- Rôles de base de données
CREATE ROLE authenticated_user;
CREATE ROLE admin_user;

-- Permissions pour authenticated_user
GRANT SELECT, UPDATE ON secure_users TO authenticated_user;
GRANT SELECT ON login_audit TO authenticated_user;
GRANT SELECT, INSERT, UPDATE ON secure_sessions TO authenticated_user;

-- Permissions pour admin_user
GRANT ALL ON ALL TABLES IN SCHEMA public TO admin_user;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO admin_user;

-- Créer l'utilisateur administrateur par défaut
INSERT INTO secure_users (
    id,
    username,
    email,
    password_hash,
    role,
    is_active,
    password_changed_at,
    created_at,
    updated_at
) VALUES (
    uuid_generate_v4(),
    'admin',
    '<EMAIL>',
    '$argon2id$v=19$m=65536,t=3,p=4$placeholder', -- À remplacer par un vrai hash
    'admin',
    true,
    NOW(),
    NOW(),
    NOW()
) ON CONFLICT (username) DO NOTHING;

-- Commentaires pour la documentation
COMMENT ON TABLE secure_users IS 'Table des utilisateurs avec sécurité renforcée et audit complet';
COMMENT ON TABLE login_audit IS 'Audit de toutes les tentatives de connexion pour détection d''intrusion';
COMMENT ON TABLE secure_sessions IS 'Sessions actives avec gestion JWT et révocation';
COMMENT ON TABLE revoked_tokens IS 'Blacklist des tokens JWT révoqués';

COMMENT ON COLUMN secure_users.password_hash IS 'Hash Argon2 du mot de passe';
COMMENT ON COLUMN secure_users.failed_login_attempts IS 'Compteur pour protection contre brute force';
COMMENT ON COLUMN secure_users.account_locked_until IS 'Verrouillage temporaire du compte';

-- Finalisation
SELECT 'Migration 001 appliquée avec succès - Tables d''authentification créées' as status;
