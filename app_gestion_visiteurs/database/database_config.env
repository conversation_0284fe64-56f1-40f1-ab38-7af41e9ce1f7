# =====================================================
# Configuration Base de Données DCOP 413
# Variables d'environnement pour l'application
# =====================================================

# Informations de base
DATABASE_NAME=dcop413_visiteurs_db
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_SCHEMA=visiteurs_schema

# Utilisateur Administrateur (pour les tâches d'administration)
ADMIN_DATABASE_USER=dcop413_admin
ADMIN_DATABASE_PASSWORD=DCOP413_SecurePass_2025!
ADMIN_DATABASE_URL=postgresql://dcop413_admin:DCOP413_SecurePass_2025!@localhost:5432/dcop413_visiteurs_db

# Utilisateur Application (pour l'application web)
APP_DATABASE_USER=dcop413_app
APP_DATABASE_PASSWORD=App_DCOP413_2025#Secure
APP_DATABASE_URL=postgresql://dcop413_app:App_DCOP413_2025#Secure@localhost:5432/dcop413_visiteurs_db

# Configuration de connexion
DATABASE_MAX_CONNECTIONS=50
DATABASE_CONNECTION_TIMEOUT=30
DATABASE_IDLE_TIMEOUT=600
DATABASE_SSL_MODE=prefer

# Configuration de sécurité
DATABASE_ENABLE_LOGGING=true
DATABASE_LOG_LEVEL=info
DATABASE_AUDIT_ENABLED=true

# Chemins pour les fichiers
PHOTOS_UPLOAD_PATH=/var/dcop413/uploads/photos
DOCUMENTS_UPLOAD_PATH=/var/dcop413/uploads/documents
BACKUP_PATH=/var/dcop413/backups

# Comptes de test (à supprimer en production)
TEST_ADMIN_USERNAME=admin
TEST_ADMIN_PASSWORD=Admin_DCOP413_2025!
TEST_AGENT_USERNAME=agent01
TEST_AGENT_PASSWORD=Agent_DCOP413_2025!
TEST_SECURITY_USERNAME=securite01
TEST_SECURITY_PASSWORD=Securite_DCOP413_2025!

# Configuration de session
SESSION_TIMEOUT=28800  # 8 heures en secondes
INACTIVITY_TIMEOUT=1800  # 30 minutes en secondes
SESSION_CLEANUP_INTERVAL=3600  # 1 heure en secondes

# Configuration de sécurité avancée
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=true
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCKOUT_DURATION=1800  # 30 minutes en secondes

# Configuration de l'audit
AUDIT_LOG_RETENTION_DAYS=365
AUDIT_LOG_MAX_SIZE_MB=100
AUDIT_SENSITIVE_OPERATIONS=true

# Configuration des notifications
EMAIL_NOTIFICATIONS_ENABLED=false
SMS_NOTIFICATIONS_ENABLED=false

# Configuration de développement (à désactiver en production)
DEBUG_MODE=false
DEVELOPMENT_MODE=false
ENABLE_SQL_LOGGING=false
