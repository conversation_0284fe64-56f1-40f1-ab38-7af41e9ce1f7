-- =====================================================
-- Script de Création Base de Données DCOP 413
-- Base de Données Ultra Sécurisée pour Gestion Visiteurs
-- =====================================================

-- Connexion en tant que superuser postgres requis
-- Commande : psql -U postgres -f create_dcop413_database.sql

-- =====================================================
-- 1. CRÉATION DE LA BASE DE DONNÉES ET UTILISATEURS
-- =====================================================

-- Créer la base de données
CREATE DATABASE dcop413_visiteurs_db
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'fr_FR.UTF-8'
    LC_CTYPE = 'fr_FR.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = 100;

-- Créer l'utilisateur administrateur
CREATE USER dcop413_admin WITH
    LOGIN
    SUPERUSER
    CREATEDB
    CREATEROLE
    INHERIT
    REPLICATION
    CONNECTION LIMIT 10
    PASSWORD 'DCOP413_SecurePass_2025!';

-- Créer l'utilisateur application (privilèges limités)
CREATE USER dcop413_app WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    INHERIT
    NOREPLICATION
    CONNECTION LIMIT 50
    PASSWORD 'App_DCOP413_2025#Secure';

-- Accorder les privilèges
GRANT ALL PRIVILEGES ON DATABASE dcop413_visiteurs_db TO dcop413_admin;
GRANT CONNECT ON DATABASE dcop413_visiteurs_db TO dcop413_app;

-- Se connecter à la base de données
\c dcop413_visiteurs_db dcop413_admin;

-- =====================================================
-- 2. CRÉATION DU SCHÉMA ET EXTENSIONS
-- =====================================================

-- Créer le schéma
CREATE SCHEMA IF NOT EXISTS visiteurs_schema AUTHORIZATION dcop413_admin;

-- Accorder les privilèges sur le schéma
GRANT USAGE ON SCHEMA visiteurs_schema TO dcop413_app;
GRANT CREATE ON SCHEMA visiteurs_schema TO dcop413_app;

-- Définir le schéma par défaut
ALTER USER dcop413_admin SET search_path = visiteurs_schema, public;
ALTER USER dcop413_app SET search_path = visiteurs_schema, public;

-- Activer les extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- =====================================================
-- 3. CRÉATION DES TABLES PRINCIPALES
-- =====================================================

-- Table des utilisateurs système
CREATE TABLE visiteurs_schema.utilisateurs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nom_utilisateur VARCHAR(50) UNIQUE NOT NULL,
    mot_de_passe_hash TEXT NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'agent', 'securite', 'reception')),
    prenom VARCHAR(50) NOT NULL,
    nom VARCHAR(50) NOT NULL,
    telephone VARCHAR(20),
    actif BOOLEAN DEFAULT TRUE,
    derniere_connexion TIMESTAMP,
    tentatives_connexion INTEGER DEFAULT 0,
    compte_verrouille BOOLEAN DEFAULT FALSE,
    verrouille_jusqu TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES visiteurs_schema.utilisateurs(id)
);

-- Table des organisations/entreprises
CREATE TABLE visiteurs_schema.organisations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nom VARCHAR(100) NOT NULL UNIQUE,
    type_organisation VARCHAR(50) CHECK (type_organisation IN ('entreprise', 'ong', 'gouvernement', 'international', 'autre')),
    secteur_activite VARCHAR(100),
    adresse_complete TEXT,
    ville VARCHAR(50),
    pays VARCHAR(50),
    telephone VARCHAR(20),
    email VARCHAR(100),
    site_web VARCHAR(200),
    contact_principal VARCHAR(100),
    statut VARCHAR(20) DEFAULT 'actif' CHECK (statut IN ('actif', 'suspendu', 'blackliste')),
    niveau_confiance INTEGER DEFAULT 1 CHECK (niveau_confiance BETWEEN 1 AND 5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des employés/personnes à rencontrer
CREATE TABLE visiteurs_schema.employes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nom VARCHAR(50) NOT NULL,
    prenom VARCHAR(50) NOT NULL,
    fonction VARCHAR(100) NOT NULL,
    departement VARCHAR(100),
    bureau VARCHAR(20),
    telephone_interne VARCHAR(10),
    telephone_mobile VARCHAR(20),
    email VARCHAR(100) UNIQUE,
    niveau_autorisation INTEGER DEFAULT 1 CHECK (niveau_autorisation BETWEEN 1 AND 5),
    peut_recevoir_visiteurs BOOLEAN DEFAULT TRUE,
    actif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des visiteurs
CREATE TABLE visiteurs_schema.visiteurs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nom VARCHAR(50) NOT NULL,
    postnom VARCHAR(50),
    prenom VARCHAR(50) NOT NULL,
    sexe VARCHAR(10) CHECK (sexe IN ('masculin', 'feminin', 'autre')) NOT NULL,
    date_naissance DATE,
    lieu_naissance VARCHAR(100),
    nationalite VARCHAR(50) NOT NULL,
    profession VARCHAR(100),
    adresse_complete TEXT,
    ville VARCHAR(50),
    pays VARCHAR(50),
    telephone1 VARCHAR(20) NOT NULL,
    telephone2 VARCHAR(20),
    email VARCHAR(100) NOT NULL,
    piece_identite VARCHAR(30) NOT NULL CHECK (piece_identite IN ('carte_identite', 'passeport', 'permis_conduire', 'autre')),
    numero_piece VARCHAR(30) NOT NULL,
    date_expiration_piece DATE,
    autorite_delivrance VARCHAR(100),
    photo_path TEXT,
    photo_hash VARCHAR(64),
    statut VARCHAR(20) DEFAULT 'actif' CHECK (statut IN ('actif', 'suspendu', 'blackliste')),
    notes_securite TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES visiteurs_schema.utilisateurs(id)
);

-- Table des visites
CREATE TABLE visiteurs_schema.visites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    numero_visite VARCHAR(20) UNIQUE NOT NULL,
    visiteur_id UUID NOT NULL REFERENCES visiteurs_schema.visiteurs(id) ON DELETE RESTRICT,
    organisation_id UUID REFERENCES visiteurs_schema.organisations(id),
    employe_rencontre_id UUID REFERENCES visiteurs_schema.employes(id),
    personne_rencontrer VARCHAR(100) NOT NULL,
    fonction_personne VARCHAR(100),
    date_visite DATE NOT NULL,
    heure_arrivee TIME NOT NULL,
    heure_arrivee_reelle TIMESTAMP,
    heure_depart_prevue TIME,
    heure_depart_reelle TIMESTAMP,
    motif TEXT NOT NULL,
    type_visite VARCHAR(30) CHECK (type_visite IN ('officielle', 'personnelle', 'livraison', 'maintenance', 'urgence')),
    niveau_securite INTEGER DEFAULT 1 CHECK (niveau_securite BETWEEN 1 AND 5),
    statut VARCHAR(20) DEFAULT 'programmee' CHECK (statut IN ('programmee', 'en_attente', 'en_cours', 'terminee', 'annulee', 'rejetee')),
    badge_numero VARCHAR(20),
    accompagnateur_id UUID REFERENCES visiteurs_schema.employes(id),
    vehicule_plaque VARCHAR(20),
    vehicule_marque VARCHAR(50),
    objets_interdits TEXT,
    objets_autorises TEXT,
    observations TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES visiteurs_schema.utilisateurs(id),
    validated_by UUID REFERENCES visiteurs_schema.utilisateurs(id),
    validated_at TIMESTAMP
);

-- Table des consignes de sécurité
CREATE TABLE visiteurs_schema.consignes_securite (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    visite_id UUID NOT NULL REFERENCES visiteurs_schema.visites(id) ON DELETE CASCADE,
    badge_requis BOOLEAN DEFAULT TRUE,
    badge_retourne BOOLEAN DEFAULT FALSE,
    accompagnement_obligatoire BOOLEAN DEFAULT TRUE,
    interdiction_photo BOOLEAN DEFAULT TRUE,
    interdiction_enregistrement BOOLEAN DEFAULT TRUE,
    interdiction_electronique BOOLEAN DEFAULT FALSE,
    zones_autorisees TEXT[],
    zones_interdites TEXT[],
    consignes_specifiques TEXT,
    accepte_par_visiteur BOOLEAN DEFAULT FALSE,
    accepte_le TIMESTAMP,
    signature_numerique TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des photos
CREATE TABLE visiteurs_schema.photos_visiteurs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    visiteur_id UUID NOT NULL REFERENCES visiteurs_schema.visiteurs(id) ON DELETE CASCADE,
    visite_id UUID REFERENCES visiteurs_schema.visites(id),
    photo_path TEXT NOT NULL,
    photo_hash VARCHAR(64) NOT NULL,
    format VARCHAR(10) NOT NULL,
    taille_octets INTEGER NOT NULL,
    resolution VARCHAR(20),
    prise_par_camera BOOLEAN DEFAULT FALSE,
    device_info TEXT,
    ip_adresse INET,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES visiteurs_schema.utilisateurs(id)
);

-- Table d'audit/journal
CREATE TABLE visiteurs_schema.journal_audit (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(50) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(20) NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE', 'SELECT')),
    old_values JSONB,
    new_values JSONB,
    utilisateur_id UUID REFERENCES visiteurs_schema.utilisateurs(id),
    ip_adresse INET,
    user_agent TEXT,
    session_id VARCHAR(100),
    horodatage TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des sessions utilisateurs
CREATE TABLE visiteurs_schema.sessions_utilisateurs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    utilisateur_id UUID NOT NULL REFERENCES visiteurs_schema.utilisateurs(id) ON DELETE CASCADE,
    token_session VARCHAR(255) UNIQUE NOT NULL,
    ip_adresse INET NOT NULL,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    actif BOOLEAN DEFAULT TRUE
);

-- Table des tentatives de connexion
CREATE TABLE visiteurs_schema.tentatives_connexion (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nom_utilisateur VARCHAR(50),
    ip_adresse INET NOT NULL,
    user_agent TEXT,
    succes BOOLEAN NOT NULL,
    message_erreur TEXT,
    horodatage TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 4. CRÉATION DES INDEX ET CONTRAINTES
-- =====================================================

-- Index pour les performances
CREATE INDEX idx_visiteurs_nom_prenom ON visiteurs_schema.visiteurs(nom, prenom);
CREATE INDEX idx_visiteurs_email ON visiteurs_schema.visiteurs(email);
CREATE INDEX idx_visiteurs_telephone ON visiteurs_schema.visiteurs(telephone1);
CREATE INDEX idx_visiteurs_piece ON visiteurs_schema.visiteurs(piece_identite, numero_piece);
CREATE INDEX idx_visiteurs_statut ON visiteurs_schema.visiteurs(statut);

CREATE INDEX idx_visites_date ON visiteurs_schema.visites(date_visite);
CREATE INDEX idx_visites_statut ON visiteurs_schema.visites(statut);
CREATE INDEX idx_visites_visiteur ON visiteurs_schema.visites(visiteur_id);
CREATE INDEX idx_visites_numero ON visiteurs_schema.visites(numero_visite);

CREATE INDEX idx_audit_table_record ON visiteurs_schema.journal_audit(table_name, record_id);
CREATE INDEX idx_audit_utilisateur ON visiteurs_schema.journal_audit(utilisateur_id);
CREATE INDEX idx_audit_horodatage ON visiteurs_schema.journal_audit(horodatage);

CREATE INDEX idx_sessions_token ON visiteurs_schema.sessions_utilisateurs(token_session);
CREATE INDEX idx_sessions_utilisateur ON visiteurs_schema.sessions_utilisateurs(utilisateur_id);
CREATE INDEX idx_sessions_expires ON visiteurs_schema.sessions_utilisateurs(expires_at);

CREATE INDEX idx_tentatives_ip ON visiteurs_schema.tentatives_connexion(ip_adresse);
CREATE INDEX idx_tentatives_utilisateur ON visiteurs_schema.tentatives_connexion(nom_utilisateur);
CREATE INDEX idx_tentatives_horodatage ON visiteurs_schema.tentatives_connexion(horodatage);

-- =====================================================
-- 5. FONCTIONS DE SÉCURITÉ
-- =====================================================

-- Fonction pour hasher les mots de passe
CREATE OR REPLACE FUNCTION visiteurs_schema.hash_password(password TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN crypt(password, gen_salt('bf', 12));
END;
$$ LANGUAGE plpgsql;

-- Fonction pour vérifier les mots de passe
CREATE OR REPLACE FUNCTION visiteurs_schema.verify_password(password TEXT, hash TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN hash = crypt(password, hash);
END;
$$ LANGUAGE plpgsql;

-- Fonction pour générer un numéro de visite unique
CREATE OR REPLACE FUNCTION visiteurs_schema.generate_numero_visite()
RETURNS TEXT AS $$
DECLARE
    numero TEXT;
    existe BOOLEAN;
BEGIN
    LOOP
        numero := 'VIS' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
        SELECT EXISTS(SELECT 1 FROM visiteurs_schema.visites WHERE numero_visite = numero) INTO existe;
        EXIT WHEN NOT existe;
    END LOOP;
    RETURN numero;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour nettoyer les sessions expirées
CREATE OR REPLACE FUNCTION visiteurs_schema.cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM visiteurs_schema.sessions_utilisateurs
    WHERE expires_at < CURRENT_TIMESTAMP OR last_activity < CURRENT_TIMESTAMP - INTERVAL '30 minutes';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. TRIGGERS D'AUDIT AUTOMATIQUE
-- =====================================================

-- Fonction pour l'audit automatique
CREATE OR REPLACE FUNCTION visiteurs_schema.audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO visiteurs_schema.journal_audit (
            table_name, record_id, action, old_values, utilisateur_id, horodatage
        ) VALUES (
            TG_TABLE_NAME, OLD.id, TG_OP, row_to_json(OLD),
            COALESCE(current_setting('app.current_user_id', true)::UUID, NULL),
            CURRENT_TIMESTAMP
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO visiteurs_schema.journal_audit (
            table_name, record_id, action, old_values, new_values, utilisateur_id, horodatage
        ) VALUES (
            TG_TABLE_NAME, NEW.id, TG_OP, row_to_json(OLD), row_to_json(NEW),
            COALESCE(current_setting('app.current_user_id', true)::UUID, NULL),
            CURRENT_TIMESTAMP
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO visiteurs_schema.journal_audit (
            table_name, record_id, action, new_values, utilisateur_id, horodatage
        ) VALUES (
            TG_TABLE_NAME, NEW.id, TG_OP, row_to_json(NEW),
            COALESCE(current_setting('app.current_user_id', true)::UUID, NULL),
            CURRENT_TIMESTAMP
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Appliquer les triggers d'audit
CREATE TRIGGER audit_visiteurs
    AFTER INSERT OR UPDATE OR DELETE ON visiteurs_schema.visiteurs
    FOR EACH ROW EXECUTE FUNCTION visiteurs_schema.audit_trigger_function();

CREATE TRIGGER audit_visites
    AFTER INSERT OR UPDATE OR DELETE ON visiteurs_schema.visites
    FOR EACH ROW EXECUTE FUNCTION visiteurs_schema.audit_trigger_function();

CREATE TRIGGER audit_utilisateurs
    AFTER INSERT OR UPDATE OR DELETE ON visiteurs_schema.utilisateurs
    FOR EACH ROW EXECUTE FUNCTION visiteurs_schema.audit_trigger_function();
