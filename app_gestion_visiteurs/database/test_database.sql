-- =====================================================
-- Script de Test Base de Données DCOP 413
-- Tests complets de fonctionnalité et sécurité
-- =====================================================

-- Connexion à la base de données
\c dcop413_visiteurs_db dcop413_admin;

-- =====================================================
-- TESTS DE FONCTIONNALITÉ
-- =====================================================

\echo '🧪 DÉBUT DES TESTS DE LA BASE DE DONNÉES DCOP 413'
\echo '=================================================='

-- Test 1: Vérification des tables
\echo '📋 Test 1: Vérification des tables créées'
SELECT 
    table_name,
    CASE 
        WHEN table_name IS NOT NULL THEN '✅ OK'
        ELSE '❌ MANQUANT'
    END as statut
FROM information_schema.tables 
WHERE table_schema = 'visiteurs_schema'
ORDER BY table_name;

-- Test 2: Vérification des utilisateurs
\echo ''
\echo '👥 Test 2: Vérification des utilisateurs système'
SELECT 
    nom_utilisateur,
    role,
    actif,
    CASE 
        WHEN actif = true THEN '✅ ACTIF'
        ELSE '❌ INACTIF'
    END as statut
FROM visiteurs_schema.utilisateurs
ORDER BY role, nom_utilisateur;

-- Test 3: Test des fonctions de sécurité
\echo ''
\echo '🔐 Test 3: Test des fonctions de sécurité'

-- Test du hashage de mot de passe
DO $$
DECLARE
    test_password TEXT := 'TestPassword123!';
    hashed_password TEXT;
    verification_result BOOLEAN;
BEGIN
    -- Hasher le mot de passe
    SELECT visiteurs_schema.hash_password(test_password) INTO hashed_password;
    
    -- Vérifier le mot de passe
    SELECT visiteurs_schema.verify_password(test_password, hashed_password) INTO verification_result;
    
    IF verification_result THEN
        RAISE NOTICE '✅ Fonction hash_password/verify_password: OK';
    ELSE
        RAISE NOTICE '❌ Fonction hash_password/verify_password: ÉCHEC';
    END IF;
END $$;

-- Test de génération de numéro de visite
DO $$
DECLARE
    numero1 TEXT;
    numero2 TEXT;
BEGIN
    SELECT visiteurs_schema.generate_numero_visite() INTO numero1;
    SELECT visiteurs_schema.generate_numero_visite() INTO numero2;
    
    IF numero1 != numero2 AND LENGTH(numero1) > 10 THEN
        RAISE NOTICE '✅ Fonction generate_numero_visite: OK (% et %)', numero1, numero2;
    ELSE
        RAISE NOTICE '❌ Fonction generate_numero_visite: ÉCHEC';
    END IF;
END $$;

-- Test 4: Vérification des données de test
\echo ''
\echo '📊 Test 4: Vérification des données de test'

SELECT 
    'Organisations' as type_donnee,
    COUNT(*) as nombre,
    CASE 
        WHEN COUNT(*) >= 4 THEN '✅ OK'
        ELSE '❌ INSUFFISANT'
    END as statut
FROM visiteurs_schema.organisations

UNION ALL

SELECT 
    'Employés' as type_donnee,
    COUNT(*) as nombre,
    CASE 
        WHEN COUNT(*) >= 3 THEN '✅ OK'
        ELSE '❌ INSUFFISANT'
    END as statut
FROM visiteurs_schema.employes

UNION ALL

SELECT 
    'Visiteurs' as type_donnee,
    COUNT(*) as nombre,
    CASE 
        WHEN COUNT(*) >= 3 THEN '✅ OK'
        ELSE '❌ INSUFFISANT'
    END as statut
FROM visiteurs_schema.visiteurs

UNION ALL

SELECT 
    'Visites' as type_donnee,
    COUNT(*) as nombre,
    CASE 
        WHEN COUNT(*) >= 3 THEN '✅ OK'
        ELSE '❌ INSUFFISANT'
    END as statut
FROM visiteurs_schema.visites;

-- Test 5: Test des vues
\echo ''
\echo '👁️ Test 5: Test des vues créées'

-- Test vue visites complètes
SELECT 
    'vue_visites_completes' as vue,
    COUNT(*) as nombre_lignes,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ OK'
        ELSE '❌ VIDE'
    END as statut
FROM visiteurs_schema.vue_visites_completes

UNION ALL

-- Test vue statistiques quotidiennes
SELECT 
    'vue_stats_quotidiennes' as vue,
    COUNT(*) as nombre_lignes,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ OK'
        ELSE '❌ VIDE'
    END as statut
FROM visiteurs_schema.vue_stats_quotidiennes;

-- Test 6: Test des index
\echo ''
\echo '🔍 Test 6: Vérification des index'
SELECT 
    indexname,
    tablename,
    '✅ OK' as statut
FROM pg_indexes 
WHERE schemaname = 'visiteurs_schema'
ORDER BY tablename, indexname;

-- =====================================================
-- TESTS DE SÉCURITÉ
-- =====================================================

\echo ''
\echo '🛡️ TESTS DE SÉCURITÉ'
\echo '===================='

-- Test 7: Vérification des privilèges utilisateur application
\echo ''
\echo '🔐 Test 7: Vérification des privilèges utilisateur application'

-- Se connecter en tant qu'utilisateur application pour tester
\c dcop413_visiteurs_db dcop413_app;

-- Test de lecture (doit fonctionner)
DO $$
DECLARE
    test_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO test_count FROM visiteurs_schema.visiteurs;
    RAISE NOTICE '✅ Lecture visiteurs: OK (% enregistrements)', test_count;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Lecture visiteurs: ÉCHEC - %', SQLERRM;
END $$;

-- Test d'insertion (doit fonctionner)
DO $$
BEGIN
    INSERT INTO visiteurs_schema.visiteurs (
        nom, prenom, sexe, nationalite, telephone1, email, 
        piece_identite, numero_piece
    ) VALUES (
        'Test', 'Utilisateur', 'masculin', 'Test', '+243000000999',
        '<EMAIL>', 'carte_identite', 'TEST123456'
    );
    RAISE NOTICE '✅ Insertion visiteur: OK';
    
    -- Nettoyer le test
    DELETE FROM visiteurs_schema.visiteurs WHERE nom = 'Test' AND prenom = 'Utilisateur';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Insertion visiteur: ÉCHEC - %', SQLERRM;
END $$;

-- Test de suppression sur journal_audit (ne doit PAS fonctionner)
DO $$
BEGIN
    DELETE FROM visiteurs_schema.journal_audit WHERE id = '00000000-0000-0000-0000-000000000000';
    RAISE NOTICE '❌ Suppression audit: PROBLÈME DE SÉCURITÉ - Suppression autorisée!';
EXCEPTION
    WHEN insufficient_privilege THEN
        RAISE NOTICE '✅ Suppression audit: OK - Accès refusé comme attendu';
    WHEN OTHERS THEN
        RAISE NOTICE '⚠️ Suppression audit: Erreur inattendue - %', SQLERRM;
END $$;

-- Retour en mode admin pour les tests suivants
\c dcop413_visiteurs_db dcop413_admin;

-- Test 8: Test des triggers d'audit
\echo ''
\echo '📝 Test 8: Test des triggers d'audit'

-- Compter les entrées d'audit avant
DO $$
DECLARE
    audit_count_before INTEGER;
    audit_count_after INTEGER;
    test_visiteur_id UUID;
BEGIN
    SELECT COUNT(*) INTO audit_count_before FROM visiteurs_schema.journal_audit;
    
    -- Insérer un visiteur de test
    INSERT INTO visiteurs_schema.visiteurs (
        nom, prenom, sexe, nationalite, telephone1, email, 
        piece_identite, numero_piece
    ) VALUES (
        'AuditTest', 'Utilisateur', 'masculin', 'Test', '+************',
        '<EMAIL>', 'carte_identite', 'AUDIT123456'
    ) RETURNING id INTO test_visiteur_id;
    
    -- Modifier le visiteur
    UPDATE visiteurs_schema.visiteurs 
    SET telephone1 = '+************' 
    WHERE id = test_visiteur_id;
    
    -- Supprimer le visiteur
    DELETE FROM visiteurs_schema.visiteurs WHERE id = test_visiteur_id;
    
    -- Compter les entrées d'audit après
    SELECT COUNT(*) INTO audit_count_after FROM visiteurs_schema.journal_audit;
    
    IF audit_count_after >= audit_count_before + 3 THEN
        RAISE NOTICE '✅ Triggers d''audit: OK (% nouvelles entrées)', audit_count_after - audit_count_before;
    ELSE
        RAISE NOTICE '❌ Triggers d''audit: ÉCHEC - Pas assez d''entrées créées';
    END IF;
END $$;

-- Test 9: Test de nettoyage des sessions
\echo ''
\echo '🧹 Test 9: Test de nettoyage des sessions'

DO $$
DECLARE
    cleaned_sessions INTEGER;
BEGIN
    -- Insérer une session expirée pour le test
    INSERT INTO visiteurs_schema.sessions_utilisateurs (
        utilisateur_id, token_session, ip_adresse, expires_at, last_activity
    ) VALUES (
        (SELECT id FROM visiteurs_schema.utilisateurs LIMIT 1),
        'test_expired_session_token',
        '127.0.0.1',
        CURRENT_TIMESTAMP - INTERVAL '1 hour',
        CURRENT_TIMESTAMP - INTERVAL '2 hours'
    );
    
    -- Nettoyer les sessions expirées
    SELECT visiteurs_schema.cleanup_expired_sessions() INTO cleaned_sessions;
    
    RAISE NOTICE '✅ Nettoyage sessions: OK (% sessions nettoyées)', cleaned_sessions;
END $$;

-- =====================================================
-- RÉSUMÉ DES TESTS
-- =====================================================

\echo ''
\echo '📊 RÉSUMÉ DES TESTS'
\echo '=================='

-- Statistiques finales
SELECT 
    'Tables créées' as element,
    COUNT(*) as nombre
FROM information_schema.tables 
WHERE table_schema = 'visiteurs_schema'

UNION ALL

SELECT 
    'Index créés' as element,
    COUNT(*) as nombre
FROM pg_indexes 
WHERE schemaname = 'visiteurs_schema'

UNION ALL

SELECT 
    'Fonctions créées' as element,
    COUNT(*) as nombre
FROM information_schema.routines 
WHERE routine_schema = 'visiteurs_schema'

UNION ALL

SELECT 
    'Triggers créés' as element,
    COUNT(*) as nombre
FROM information_schema.triggers 
WHERE trigger_schema = 'visiteurs_schema';

\echo ''
\echo '🎉 TESTS TERMINÉS'
\echo '================'
\echo 'Vérifiez les résultats ci-dessus.'
\echo 'Tous les éléments marqués ✅ OK sont fonctionnels.'
\echo 'Les éléments marqués ❌ nécessitent une attention.'
\echo ''
