# 🗄️ Base de Données Ultra Sécurisée - DCOP 413

## 📋 **Informations de Connexion**

### 🔐 **Détails de la Base de Données :**
- **Nom de la BD** : `dcop413_visiteurs_db`
- **Utilisateur Principal** : `dcop413_admin`
- **Mot de passe** : `DCOP413_SecurePass_2025!`
- **Utilisateur Application** : `dcop413_app`
- **Mot de passe App** : `App_DCOP413_2025#Secure`
- **Host** : `localhost`
- **Port** : `5432`
- **Schéma** : `visiteurs_schema`

### 🔗 **Chaînes de Connexion :**

#### **Pour l'Administration :**
```
postgresql://dcop413_admin:DCOP413_SecurePass_2025!@localhost:5432/dcop413_visiteurs_db
```

#### **Pour l'Application :**
```
postgresql://dcop413_app:App_DCOP413_2025#Secure@localhost:5432/dcop413_visiteurs_db
```

## 🛠️ **Script de Création Complet**

### 1. **Création de la Base de Données et des Utilisateurs**

```sql
-- Connexion en tant que superuser postgres
-- psql -U postgres

-- Créer la base de données
CREATE DATABASE dcop413_visiteurs_db
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'fr_FR.UTF-8'
    LC_CTYPE = 'fr_FR.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = 100;

-- Créer l'utilisateur administrateur
CREATE USER dcop413_admin WITH
    LOGIN
    SUPERUSER
    CREATEDB
    CREATEROLE
    INHERIT
    REPLICATION
    CONNECTION LIMIT 10
    PASSWORD 'DCOP413_SecurePass_2025!';

-- Créer l'utilisateur application (privilèges limités)
CREATE USER dcop413_app WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    INHERIT
    NOREPLICATION
    CONNECTION LIMIT 50
    PASSWORD 'App_DCOP413_2025#Secure';

-- Accorder les privilèges
GRANT ALL PRIVILEGES ON DATABASE dcop413_visiteurs_db TO dcop413_admin;
GRANT CONNECT ON DATABASE dcop413_visiteurs_db TO dcop413_app;

-- Se connecter à la base de données
\c dcop413_visiteurs_db dcop413_admin;

-- Créer le schéma
CREATE SCHEMA IF NOT EXISTS visiteurs_schema AUTHORIZATION dcop413_admin;

-- Accorder les privilèges sur le schéma
GRANT USAGE ON SCHEMA visiteurs_schema TO dcop413_app;
GRANT CREATE ON SCHEMA visiteurs_schema TO dcop413_app;

-- Définir le schéma par défaut
ALTER USER dcop413_admin SET search_path = visiteurs_schema, public;
ALTER USER dcop413_app SET search_path = visiteurs_schema, public;
```

### 2. **Extensions de Sécurité**

```sql
-- Activer les extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Extension pour l'audit (si disponible)
-- CREATE EXTENSION IF NOT EXISTS "pg_audit";
```

### 3. **Tables Principales**

```sql
-- Table des utilisateurs système
CREATE TABLE visiteurs_schema.utilisateurs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nom_utilisateur VARCHAR(50) UNIQUE NOT NULL,
    mot_de_passe_hash TEXT NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'agent', 'securite', 'reception')),
    prenom VARCHAR(50) NOT NULL,
    nom VARCHAR(50) NOT NULL,
    telephone VARCHAR(20),
    actif BOOLEAN DEFAULT TRUE,
    derniere_connexion TIMESTAMP,
    tentatives_connexion INTEGER DEFAULT 0,
    compte_verrouille BOOLEAN DEFAULT FALSE,
    verrouille_jusqu TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES visiteurs_schema.utilisateurs(id)
);

-- Table des visiteurs
CREATE TABLE visiteurs_schema.visiteurs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nom VARCHAR(50) NOT NULL,
    postnom VARCHAR(50),
    prenom VARCHAR(50) NOT NULL,
    sexe VARCHAR(10) CHECK (sexe IN ('masculin', 'feminin', 'autre')) NOT NULL,
    date_naissance DATE,
    lieu_naissance VARCHAR(100),
    nationalite VARCHAR(50) NOT NULL,
    profession VARCHAR(100),
    adresse_complete TEXT,
    ville VARCHAR(50),
    pays VARCHAR(50),
    telephone1 VARCHAR(20) NOT NULL,
    telephone2 VARCHAR(20),
    email VARCHAR(100) NOT NULL,
    piece_identite VARCHAR(30) NOT NULL CHECK (piece_identite IN ('carte_identite', 'passeport', 'permis_conduire', 'autre')),
    numero_piece VARCHAR(30) NOT NULL,
    date_expiration_piece DATE,
    autorite_delivrance VARCHAR(100),
    photo_path TEXT,
    photo_hash VARCHAR(64),
    statut VARCHAR(20) DEFAULT 'actif' CHECK (statut IN ('actif', 'suspendu', 'blackliste')),
    notes_securite TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES visiteurs_schema.utilisateurs(id)
);

-- Table des organisations/entreprises
CREATE TABLE visiteurs_schema.organisations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nom VARCHAR(100) NOT NULL UNIQUE,
    type_organisation VARCHAR(50) CHECK (type_organisation IN ('entreprise', 'ong', 'gouvernement', 'international', 'autre')),
    secteur_activite VARCHAR(100),
    adresse_complete TEXT,
    ville VARCHAR(50),
    pays VARCHAR(50),
    telephone VARCHAR(20),
    email VARCHAR(100),
    site_web VARCHAR(200),
    contact_principal VARCHAR(100),
    statut VARCHAR(20) DEFAULT 'actif' CHECK (statut IN ('actif', 'suspendu', 'blackliste')),
    niveau_confiance INTEGER DEFAULT 1 CHECK (niveau_confiance BETWEEN 1 AND 5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des employés/personnes à rencontrer
CREATE TABLE visiteurs_schema.employes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nom VARCHAR(50) NOT NULL,
    prenom VARCHAR(50) NOT NULL,
    fonction VARCHAR(100) NOT NULL,
    departement VARCHAR(100),
    bureau VARCHAR(20),
    telephone_interne VARCHAR(10),
    telephone_mobile VARCHAR(20),
    email VARCHAR(100) UNIQUE,
    niveau_autorisation INTEGER DEFAULT 1 CHECK (niveau_autorisation BETWEEN 1 AND 5),
    peut_recevoir_visiteurs BOOLEAN DEFAULT TRUE,
    actif BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des visites
CREATE TABLE visiteurs_schema.visites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    numero_visite VARCHAR(20) UNIQUE NOT NULL,
    visiteur_id UUID NOT NULL REFERENCES visiteurs_schema.visiteurs(id) ON DELETE RESTRICT,
    organisation_id UUID REFERENCES visiteurs_schema.organisations(id),
    employe_rencontre_id UUID REFERENCES visiteurs_schema.employes(id),
    personne_rencontrer VARCHAR(100) NOT NULL,
    fonction_personne VARCHAR(100),
    date_visite DATE NOT NULL,
    heure_arrivee TIME NOT NULL,
    heure_arrivee_reelle TIMESTAMP,
    heure_depart_prevue TIME,
    heure_depart_reelle TIMESTAMP,
    motif TEXT NOT NULL,
    type_visite VARCHAR(30) CHECK (type_visite IN ('officielle', 'personnelle', 'livraison', 'maintenance', 'urgence')),
    niveau_securite INTEGER DEFAULT 1 CHECK (niveau_securite BETWEEN 1 AND 5),
    statut VARCHAR(20) DEFAULT 'programmee' CHECK (statut IN ('programmee', 'en_attente', 'en_cours', 'terminee', 'annulee', 'rejetee')),
    badge_numero VARCHAR(20),
    accompagnateur_id UUID REFERENCES visiteurs_schema.employes(id),
    vehicule_plaque VARCHAR(20),
    vehicule_marque VARCHAR(50),
    objets_interdits TEXT,
    objets_autorises TEXT,
    observations TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES visiteurs_schema.utilisateurs(id),
    validated_by UUID REFERENCES visiteurs_schema.utilisateurs(id),
    validated_at TIMESTAMP
);
```

### 4. **Tables de Sécurité et Audit**

```sql
-- Table des consignes de sécurité
CREATE TABLE visiteurs_schema.consignes_securite (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    visite_id UUID NOT NULL REFERENCES visiteurs_schema.visites(id) ON DELETE CASCADE,
    badge_requis BOOLEAN DEFAULT TRUE,
    badge_retourne BOOLEAN DEFAULT FALSE,
    accompagnement_obligatoire BOOLEAN DEFAULT TRUE,
    interdiction_photo BOOLEAN DEFAULT TRUE,
    interdiction_enregistrement BOOLEAN DEFAULT TRUE,
    interdiction_electronique BOOLEAN DEFAULT FALSE,
    zones_autorisees TEXT[],
    zones_interdites TEXT[],
    consignes_specifiques TEXT,
    accepte_par_visiteur BOOLEAN DEFAULT FALSE,
    accepte_le TIMESTAMP,
    signature_numerique TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des photos
CREATE TABLE visiteurs_schema.photos_visiteurs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    visiteur_id UUID NOT NULL REFERENCES visiteurs_schema.visiteurs(id) ON DELETE CASCADE,
    visite_id UUID REFERENCES visiteurs_schema.visites(id),
    photo_path TEXT NOT NULL,
    photo_hash VARCHAR(64) NOT NULL,
    format VARCHAR(10) NOT NULL,
    taille_octets INTEGER NOT NULL,
    resolution VARCHAR(20),
    prise_par_camera BOOLEAN DEFAULT FALSE,
    device_info TEXT,
    ip_adresse INET,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES visiteurs_schema.utilisateurs(id)
);

-- Table d'audit/journal
CREATE TABLE visiteurs_schema.journal_audit (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(50) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(20) NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE', 'SELECT')),
    old_values JSONB,
    new_values JSONB,
    utilisateur_id UUID REFERENCES visiteurs_schema.utilisateurs(id),
    ip_adresse INET,
    user_agent TEXT,
    session_id VARCHAR(100),
    horodatage TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des sessions utilisateurs
CREATE TABLE visiteurs_schema.sessions_utilisateurs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    utilisateur_id UUID NOT NULL REFERENCES visiteurs_schema.utilisateurs(id) ON DELETE CASCADE,
    token_session VARCHAR(255) UNIQUE NOT NULL,
    ip_adresse INET NOT NULL,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    actif BOOLEAN DEFAULT TRUE
);

-- Table des tentatives de connexion
CREATE TABLE visiteurs_schema.tentatives_connexion (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nom_utilisateur VARCHAR(50),
    ip_adresse INET NOT NULL,
    user_agent TEXT,
    succes BOOLEAN NOT NULL,
    message_erreur TEXT,
    horodatage TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 5. **Index et Contraintes de Performance**

```sql
-- Index pour les performances
CREATE INDEX idx_visiteurs_nom_prenom ON visiteurs_schema.visiteurs(nom, prenom);
CREATE INDEX idx_visiteurs_email ON visiteurs_schema.visiteurs(email);
CREATE INDEX idx_visiteurs_telephone ON visiteurs_schema.visiteurs(telephone1);
CREATE INDEX idx_visiteurs_piece ON visiteurs_schema.visiteurs(piece_identite, numero_piece);
CREATE INDEX idx_visiteurs_statut ON visiteurs_schema.visiteurs(statut);

CREATE INDEX idx_visites_date ON visiteurs_schema.visites(date_visite);
CREATE INDEX idx_visites_statut ON visiteurs_schema.visites(statut);
CREATE INDEX idx_visites_visiteur ON visiteurs_schema.visites(visiteur_id);
CREATE INDEX idx_visites_numero ON visiteurs_schema.visites(numero_visite);

CREATE INDEX idx_audit_table_record ON visiteurs_schema.journal_audit(table_name, record_id);
CREATE INDEX idx_audit_utilisateur ON visiteurs_schema.journal_audit(utilisateur_id);
CREATE INDEX idx_audit_horodatage ON visiteurs_schema.journal_audit(horodatage);

CREATE INDEX idx_sessions_token ON visiteurs_schema.sessions_utilisateurs(token_session);
CREATE INDEX idx_sessions_utilisateur ON visiteurs_schema.sessions_utilisateurs(utilisateur_id);
CREATE INDEX idx_sessions_expires ON visiteurs_schema.sessions_utilisateurs(expires_at);

-- Index pour les tentatives de connexion
CREATE INDEX idx_tentatives_ip ON visiteurs_schema.tentatives_connexion(ip_adresse);
CREATE INDEX idx_tentatives_utilisateur ON visiteurs_schema.tentatives_connexion(nom_utilisateur);
CREATE INDEX idx_tentatives_horodatage ON visiteurs_schema.tentatives_connexion(horodatage);
```

### 6. **Triggers d'Audit Automatique**

```sql
-- Fonction pour l'audit automatique
CREATE OR REPLACE FUNCTION visiteurs_schema.audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO visiteurs_schema.journal_audit (
            table_name, record_id, action, old_values, utilisateur_id, horodatage
        ) VALUES (
            TG_TABLE_NAME, OLD.id, TG_OP, row_to_json(OLD),
            COALESCE(current_setting('app.current_user_id', true)::UUID, NULL),
            CURRENT_TIMESTAMP
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO visiteurs_schema.journal_audit (
            table_name, record_id, action, old_values, new_values, utilisateur_id, horodatage
        ) VALUES (
            TG_TABLE_NAME, NEW.id, TG_OP, row_to_json(OLD), row_to_json(NEW),
            COALESCE(current_setting('app.current_user_id', true)::UUID, NULL),
            CURRENT_TIMESTAMP
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO visiteurs_schema.journal_audit (
            table_name, record_id, action, new_values, utilisateur_id, horodatage
        ) VALUES (
            TG_TABLE_NAME, NEW.id, TG_OP, row_to_json(NEW),
            COALESCE(current_setting('app.current_user_id', true)::UUID, NULL),
            CURRENT_TIMESTAMP
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Appliquer les triggers d'audit
CREATE TRIGGER audit_visiteurs
    AFTER INSERT OR UPDATE OR DELETE ON visiteurs_schema.visiteurs
    FOR EACH ROW EXECUTE FUNCTION visiteurs_schema.audit_trigger_function();

CREATE TRIGGER audit_visites
    AFTER INSERT OR UPDATE OR DELETE ON visiteurs_schema.visites
    FOR EACH ROW EXECUTE FUNCTION visiteurs_schema.audit_trigger_function();

CREATE TRIGGER audit_utilisateurs
    AFTER INSERT OR UPDATE OR DELETE ON visiteurs_schema.utilisateurs
    FOR EACH ROW EXECUTE FUNCTION visiteurs_schema.audit_trigger_function();
```

### 7. **Fonctions de Sécurité**

```sql
-- Fonction pour hasher les mots de passe
CREATE OR REPLACE FUNCTION visiteurs_schema.hash_password(password TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN crypt(password, gen_salt('bf', 12));
END;
$$ LANGUAGE plpgsql;

-- Fonction pour vérifier les mots de passe
CREATE OR REPLACE FUNCTION visiteurs_schema.verify_password(password TEXT, hash TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN hash = crypt(password, hash);
END;
$$ LANGUAGE plpgsql;

-- Fonction pour générer un numéro de visite unique
CREATE OR REPLACE FUNCTION visiteurs_schema.generate_numero_visite()
RETURNS TEXT AS $$
DECLARE
    numero TEXT;
    existe BOOLEAN;
BEGIN
    LOOP
        numero := 'VIS' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
        SELECT EXISTS(SELECT 1 FROM visiteurs_schema.visites WHERE numero_visite = numero) INTO existe;
        EXIT WHEN NOT existe;
    END LOOP;
    RETURN numero;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour nettoyer les sessions expirées
CREATE OR REPLACE FUNCTION visiteurs_schema.cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM visiteurs_schema.sessions_utilisateurs
    WHERE expires_at < CURRENT_TIMESTAMP OR last_activity < CURRENT_TIMESTAMP - INTERVAL '30 minutes';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;
```

### 8. **Données Initiales**

```sql
-- Insérer l'utilisateur administrateur initial
INSERT INTO visiteurs_schema.utilisateurs (
    nom_utilisateur, mot_de_passe_hash, email, role, prenom, nom, telephone
) VALUES (
    'admin',
    visiteurs_schema.hash_password('Admin_DCOP413_2025!'),
    '<EMAIL>',
    'admin',
    'Administrateur',
    'Système',
    '+243000000000'
);

-- Insérer un utilisateur agent de test
INSERT INTO visiteurs_schema.utilisateurs (
    nom_utilisateur, mot_de_passe_hash, email, role, prenom, nom, telephone
) VALUES (
    'agent01',
    visiteurs_schema.hash_password('Agent_DCOP413_2025!'),
    '<EMAIL>',
    'agent',
    'Agent',
    'Réception',
    '+243000000001'
);

-- Insérer quelques organisations de test
INSERT INTO visiteurs_schema.organisations (nom, type_organisation, secteur_activite, niveau_confiance) VALUES
('Ministère des Affaires Étrangères', 'gouvernement', 'Diplomatie', 5),
('Ambassade de France', 'gouvernement', 'Diplomatie', 5),
('ONU RDC', 'international', 'Humanitaire', 4),
('Entreprise Locale SARL', 'entreprise', 'Commerce', 2);

-- Insérer quelques employés
INSERT INTO visiteurs_schema.employes (nom, prenom, fonction, departement, email, niveau_autorisation) VALUES
('Directeur', 'Général', 'Directeur Général', 'Direction', '<EMAIL>', 5),
('Chef', 'Sécurité', 'Chef de la Sécurité', 'Sécurité', '<EMAIL>', 4),
('Responsable', 'Accueil', 'Responsable Accueil', 'Réception', '<EMAIL>', 3);
```

### 9. **Privilèges et Sécurité**

```sql
-- Accorder les privilèges nécessaires à l'utilisateur application
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA visiteurs_schema TO dcop413_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA visiteurs_schema TO dcop413_app;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA visiteurs_schema TO dcop413_app;

-- Privilèges spécifiques pour certaines tables sensibles
REVOKE DELETE ON visiteurs_schema.journal_audit FROM dcop413_app;
REVOKE UPDATE ON visiteurs_schema.journal_audit FROM dcop413_app;

-- Politique de sécurité au niveau ligne (RLS) pour les données sensibles
ALTER TABLE visiteurs_schema.visiteurs ENABLE ROW LEVEL SECURITY;
ALTER TABLE visiteurs_schema.visites ENABLE ROW LEVEL SECURITY;

-- Politique pour les utilisateurs non-admin
CREATE POLICY visiteurs_policy ON visiteurs_schema.visiteurs
    FOR ALL TO dcop413_app
    USING (statut != 'blackliste' OR current_setting('app.user_role', true) = 'admin');

CREATE POLICY visites_policy ON visiteurs_schema.visites
    FOR ALL TO dcop413_app
    USING (statut != 'rejetee' OR current_setting('app.user_role', true) = 'admin');
```

### 10. **Configuration de Sauvegarde Automatique**

```sql
-- Créer une fonction de sauvegarde
CREATE OR REPLACE FUNCTION visiteurs_schema.backup_database()
RETURNS TEXT AS $$
DECLARE
    backup_file TEXT;
BEGIN
    backup_file := '/var/backups/dcop413_' || TO_CHAR(CURRENT_TIMESTAMP, 'YYYY-MM-DD_HH24-MI-SS') || '.sql';
    PERFORM pg_dump('dcop413_visiteurs_db', backup_file);
    RETURN backup_file;
END;
$$ LANGUAGE plpgsql;

-- Programmer une tâche de nettoyage quotidienne (à configurer avec cron)
-- 0 2 * * * psql -d dcop413_visiteurs_db -c "SELECT visiteurs_schema.cleanup_expired_sessions();"
```

## 🔧 **Instructions de Déploiement**

### **Étape 1 : Connexion à PostgreSQL**
```bash
# Se connecter en tant que superuser
sudo -u postgres psql

# Ou avec mot de passe
psql -U postgres -h localhost
```

### **Étape 2 : Exécuter le Script**
```bash
# Copier tout le script SQL dans un fichier
nano /tmp/dcop413_setup.sql

# Exécuter le script
psql -U postgres -f /tmp/dcop413_setup.sql
```

### **Étape 3 : Vérification**
```sql
-- Se connecter à la nouvelle base
\c dcop413_visiteurs_db dcop413_admin

-- Vérifier les tables
\dt visiteurs_schema.*

-- Tester la connexion application
\c dcop413_visiteurs_db dcop413_app

-- Vérifier les privilèges
\dp visiteurs_schema.*
```

## 🔐 **Informations de Sécurité Complètes**

### **Comptes Utilisateurs :**
1. **dcop413_admin** : Accès complet (administration)
2. **dcop413_app** : Accès limité (application)

### **Mots de Passe :**
- **Admin** : `DCOP413_SecurePass_2025!`
- **App** : `App_DCOP413_2025#Secure`
- **Utilisateur test admin** : `Admin_DCOP413_2025!`
- **Utilisateur test agent** : `Agent_DCOP413_2025!`

### **Fonctionnalités de Sécurité :**
- ✅ Chiffrement des mots de passe (bcrypt)
- ✅ Audit automatique de toutes les modifications
- ✅ Gestion des sessions avec expiration
- ✅ Politique de sécurité au niveau ligne (RLS)
- ✅ Index optimisés pour les performances
- ✅ Contraintes d'intégrité strictes
- ✅ Triggers automatiques pour l'audit
- ✅ Nettoyage automatique des sessions expirées

Cette base de données est maintenant prête pour votre application DCOP 413 avec un niveau de sécurité maximal ! 🚀🔐
```
