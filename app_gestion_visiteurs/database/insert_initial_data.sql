-- =====================================================
-- Script d'Insertion des Données Initiales - DCOP 413
-- À exécuter après create_dcop413_database.sql
-- =====================================================

-- Connexion à la base de données
\c dcop413_visiteurs_db dcop413_admin;

-- =====================================================
-- 7. DONNÉES INITIALES
-- =====================================================

-- Insérer l'utilisateur administrateur initial
INSERT INTO visiteurs_schema.utilisateurs (
    nom_utilisateur, mot_de_passe_hash, email, role, prenom, nom, telephone
) VALUES (
    'admin', 
    visiteurs_schema.hash_password('Admin_DCOP413_2025!'),
    '<EMAIL>',
    'admin',
    'Administrateur',
    'Système',
    '+243000000000'
);

-- Insérer un utilisateur agent de test
INSERT INTO visiteurs_schema.utilisateurs (
    nom_utilisateur, mot_de_passe_hash, email, role, prenom, nom, telephone
) VALUES (
    'agent01', 
    visiteurs_schema.hash_password('Agent_DCOP413_2025!'),
    '<EMAIL>',
    'agent',
    'Agent',
    'Réception',
    '+243000000001'
);

-- Insérer un utilisateur sécurité
INSERT INTO visiteurs_schema.utilisateurs (
    nom_utilisateur, mot_de_passe_hash, email, role, prenom, nom, telephone
) VALUES (
    'securite01', 
    visiteurs_schema.hash_password('Securite_DCOP413_2025!'),
    '<EMAIL>',
    'securite',
    'Agent',
    'Sécurité',
    '+243000000002'
);

-- Insérer quelques organisations de test
INSERT INTO visiteurs_schema.organisations (nom, type_organisation, secteur_activite, niveau_confiance, ville, pays) VALUES
('Ministère des Affaires Étrangères', 'gouvernement', 'Diplomatie', 5, 'Kinshasa', 'RDC'),
('Ambassade de France', 'gouvernement', 'Diplomatie', 5, 'Kinshasa', 'RDC'),
('ONU RDC', 'international', 'Humanitaire', 4, 'Kinshasa', 'RDC'),
('Entreprise Locale SARL', 'entreprise', 'Commerce', 2, 'Kinshasa', 'RDC'),
('Banque Centrale du Congo', 'gouvernement', 'Finance', 5, 'Kinshasa', 'RDC'),
('Médecins Sans Frontières', 'ong', 'Santé', 4, 'Kinshasa', 'RDC');

-- Insérer quelques employés
INSERT INTO visiteurs_schema.employes (nom, prenom, fonction, departement, email, niveau_autorisation, telephone_mobile) VALUES
('Directeur', 'Général', 'Directeur Général', 'Direction', '<EMAIL>', 5, '+243000000100'),
('Chef', 'Sécurité', 'Chef de la Sécurité', 'Sécurité', '<EMAIL>', 4, '+243000000101'),
('Responsable', 'Accueil', 'Responsable Accueil', 'Réception', '<EMAIL>', 3, '+243000000102'),
('Directeur', 'Adjoint', 'Directeur Adjoint', 'Direction', '<EMAIL>', 4, '+243000000103'),
('Chef', 'Protocole', 'Chef du Protocole', 'Protocole', '<EMAIL>', 3, '+243000000104'),
('Responsable', 'Communication', 'Responsable Communication', 'Communication', '<EMAIL>', 2, '+243000000105');

-- Insérer quelques visiteurs de test
INSERT INTO visiteurs_schema.visiteurs (
    nom, prenom, sexe, nationalite, telephone1, email, piece_identite, numero_piece, 
    profession, ville, pays, created_by
) VALUES 
(
    'Dupont', 'Jean', 'masculin', 'Française', '+33123456789', '<EMAIL>',
    'passeport', 'FR123456789', 'Diplomate', 'Paris', 'France',
    (SELECT id FROM visiteurs_schema.utilisateurs WHERE nom_utilisateur = 'admin')
),
(
    'Smith', 'Mary', 'feminin', 'Américaine', '+1234567890', '<EMAIL>',
    'passeport', 'US987654321', 'Journaliste', 'New York', 'USA',
    (SELECT id FROM visiteurs_schema.utilisateurs WHERE nom_utilisateur = 'admin')
),
(
    'Mukendi', 'Pierre', 'masculin', 'Congolaise', '+243987654321', '<EMAIL>',
    'carte_identite', 'CD123456789', 'Entrepreneur', 'Kinshasa', 'RDC',
    (SELECT id FROM visiteurs_schema.utilisateurs WHERE nom_utilisateur = 'agent01')
);

-- Insérer quelques visites de test
INSERT INTO visiteurs_schema.visites (
    numero_visite, visiteur_id, organisation_id, employe_rencontre_id, 
    personne_rencontrer, date_visite, heure_arrivee, motif, type_visite,
    niveau_securite, statut, created_by
) VALUES 
(
    visiteurs_schema.generate_numero_visite(),
    (SELECT id FROM visiteurs_schema.visiteurs WHERE nom = 'Dupont'),
    (SELECT id FROM visiteurs_schema.organisations WHERE nom = 'Ambassade de France'),
    (SELECT id FROM visiteurs_schema.employes WHERE nom = 'Directeur' AND prenom = 'Général'),
    'Directeur Général', CURRENT_DATE + INTERVAL '1 day', '09:00:00',
    'Réunion diplomatique importante', 'officielle', 4, 'programmee',
    (SELECT id FROM visiteurs_schema.utilisateurs WHERE nom_utilisateur = 'admin')
),
(
    visiteurs_schema.generate_numero_visite(),
    (SELECT id FROM visiteurs_schema.visiteurs WHERE nom = 'Smith'),
    (SELECT id FROM visiteurs_schema.organisations WHERE nom = 'ONU RDC'),
    (SELECT id FROM visiteurs_schema.employes WHERE nom = 'Chef' AND prenom = 'Protocole'),
    'Chef du Protocole', CURRENT_DATE, '14:30:00',
    'Interview pour article de presse', 'officielle', 2, 'en_attente',
    (SELECT id FROM visiteurs_schema.utilisateurs WHERE nom_utilisateur = 'agent01')
),
(
    visiteurs_schema.generate_numero_visite(),
    (SELECT id FROM visiteurs_schema.visiteurs WHERE nom = 'Mukendi'),
    (SELECT id FROM visiteurs_schema.organisations WHERE nom = 'Entreprise Locale SARL'),
    (SELECT id FROM visiteurs_schema.employes WHERE nom = 'Responsable' AND prenom = 'Communication'),
    'Responsable Communication', CURRENT_DATE + INTERVAL '2 days', '11:00:00',
    'Présentation de projet commercial', 'personnelle', 1, 'programmee',
    (SELECT id FROM visiteurs_schema.utilisateurs WHERE nom_utilisateur = 'agent01')
);

-- Insérer les consignes de sécurité pour les visites
INSERT INTO visiteurs_schema.consignes_securite (
    visite_id, badge_requis, accompagnement_obligatoire, interdiction_photo,
    interdiction_enregistrement, zones_autorisees, consignes_specifiques
) VALUES 
(
    (SELECT id FROM visiteurs_schema.visites WHERE personne_rencontrer = 'Directeur Général'),
    TRUE, TRUE, TRUE, TRUE, 
    ARRAY['hall_accueil', 'bureau_direction', 'salle_reunion_vip'],
    'Visite de haut niveau - Protocole strict requis'
),
(
    (SELECT id FROM visiteurs_schema.visites WHERE personne_rencontrer = 'Chef du Protocole'),
    TRUE, FALSE, FALSE, FALSE,
    ARRAY['hall_accueil', 'bureau_protocole', 'salle_presse'],
    'Interview autorisée - Enregistrement permis avec autorisation'
),
(
    (SELECT id FROM visiteurs_schema.visites WHERE personne_rencontrer = 'Responsable Communication'),
    TRUE, FALSE, TRUE, TRUE,
    ARRAY['hall_accueil', 'bureau_communication'],
    'Visite commerciale standard'
);

-- =====================================================
-- 8. PRIVILÈGES ET SÉCURITÉ
-- =====================================================

-- Accorder les privilèges nécessaires à l'utilisateur application
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA visiteurs_schema TO dcop413_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA visiteurs_schema TO dcop413_app;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA visiteurs_schema TO dcop413_app;

-- Privilèges spécifiques pour certaines tables sensibles
REVOKE DELETE ON visiteurs_schema.journal_audit FROM dcop413_app;
REVOKE UPDATE ON visiteurs_schema.journal_audit FROM dcop413_app;

-- Politique de sécurité au niveau ligne (RLS) pour les données sensibles
ALTER TABLE visiteurs_schema.visiteurs ENABLE ROW LEVEL SECURITY;
ALTER TABLE visiteurs_schema.visites ENABLE ROW LEVEL SECURITY;

-- Politique pour les utilisateurs non-admin
CREATE POLICY visiteurs_policy ON visiteurs_schema.visiteurs
    FOR ALL TO dcop413_app
    USING (statut != 'blackliste' OR current_setting('app.user_role', true) = 'admin');

CREATE POLICY visites_policy ON visiteurs_schema.visites
    FOR ALL TO dcop413_app
    USING (statut != 'rejetee' OR current_setting('app.user_role', true) = 'admin');

-- =====================================================
-- 9. VUES UTILES POUR L'APPLICATION
-- =====================================================

-- Vue pour les visites avec informations complètes
CREATE VIEW visiteurs_schema.vue_visites_completes AS
SELECT 
    v.id,
    v.numero_visite,
    vis.nom || ' ' || vis.prenom AS nom_complet_visiteur,
    vis.telephone1,
    vis.email AS email_visiteur,
    vis.nationalite,
    o.nom AS organisation,
    v.personne_rencontrer,
    v.date_visite,
    v.heure_arrivee,
    v.motif,
    v.type_visite,
    v.statut,
    v.niveau_securite,
    e.nom || ' ' || e.prenom AS employe_contact,
    u.nom || ' ' || u.prenom AS cree_par,
    v.created_at
FROM visiteurs_schema.visites v
LEFT JOIN visiteurs_schema.visiteurs vis ON v.visiteur_id = vis.id
LEFT JOIN visiteurs_schema.organisations o ON v.organisation_id = o.id
LEFT JOIN visiteurs_schema.employes e ON v.employe_rencontre_id = e.id
LEFT JOIN visiteurs_schema.utilisateurs u ON v.created_by = u.id;

-- Vue pour les statistiques quotidiennes
CREATE VIEW visiteurs_schema.vue_stats_quotidiennes AS
SELECT 
    date_visite,
    COUNT(*) AS total_visites,
    COUNT(CASE WHEN statut = 'programmee' THEN 1 END) AS programmees,
    COUNT(CASE WHEN statut = 'en_attente' THEN 1 END) AS en_attente,
    COUNT(CASE WHEN statut = 'en_cours' THEN 1 END) AS en_cours,
    COUNT(CASE WHEN statut = 'terminee' THEN 1 END) AS terminees,
    COUNT(CASE WHEN statut = 'annulee' THEN 1 END) AS annulees
FROM visiteurs_schema.visites
GROUP BY date_visite
ORDER BY date_visite DESC;

-- Accorder les privilèges sur les vues
GRANT SELECT ON visiteurs_schema.vue_visites_completes TO dcop413_app;
GRANT SELECT ON visiteurs_schema.vue_stats_quotidiennes TO dcop413_app;

-- =====================================================
-- 10. MESSAGES DE CONFIRMATION
-- =====================================================

-- Afficher les informations de création
SELECT 'Base de données DCOP 413 créée avec succès!' AS message;
SELECT 'Utilisateurs créés:' AS info;
SELECT nom_utilisateur, role, email FROM visiteurs_schema.utilisateurs;
SELECT 'Organisations créées:' AS info;
SELECT nom, type_organisation FROM visiteurs_schema.organisations;
SELECT 'Employés créés:' AS info;
SELECT nom, prenom, fonction FROM visiteurs_schema.employes;
SELECT 'Visiteurs de test créés:' AS info;
SELECT nom, prenom, nationalite FROM visiteurs_schema.visiteurs;
SELECT 'Visites de test créées:' AS info;
SELECT numero_visite, personne_rencontrer, date_visite, statut FROM visiteurs_schema.visites;
