#!/bin/bash

# =====================================================
# Script de Déploiement Automatique - Base de Données DCOP 413
# Déploiement ultra sécurisé pour PostgreSQL-16
# =====================================================

set -e  # Arrêter en cas d'erreur

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_NAME="dcop413_visiteurs_db"
ADMIN_USER="dcop413_admin"
APP_USER="dcop413_app"
POSTGRES_USER="postgres"

echo -e "${BLUE}======================================================${NC}"
echo -e "${BLUE}🚀 DÉPLOIEMENT BASE DE DONNÉES DCOP 413${NC}"
echo -e "${BLUE}======================================================${NC}"

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Vérifier si PostgreSQL est installé et en cours d'exécution
check_postgresql() {
    log_info "Vérification de PostgreSQL..."
    
    if ! command -v psql &> /dev/null; then
        log_error "PostgreSQL n'est pas installé ou pas dans le PATH"
        exit 1
    fi
    
    if ! sudo systemctl is-active --quiet postgresql; then
        log_warning "PostgreSQL n'est pas en cours d'exécution. Tentative de démarrage..."
        sudo systemctl start postgresql
        sleep 2
        
        if ! sudo systemctl is-active --quiet postgresql; then
            log_error "Impossible de démarrer PostgreSQL"
            exit 1
        fi
    fi
    
    log_success "PostgreSQL est opérationnel"
}

# Vérifier les fichiers SQL
check_sql_files() {
    log_info "Vérification des fichiers SQL..."
    
    if [ ! -f "create_dcop413_database.sql" ]; then
        log_error "Fichier create_dcop413_database.sql introuvable"
        exit 1
    fi
    
    if [ ! -f "insert_initial_data.sql" ]; then
        log_error "Fichier insert_initial_data.sql introuvable"
        exit 1
    fi
    
    log_success "Fichiers SQL trouvés"
}

# Sauvegarder la base existante si elle existe
backup_existing_database() {
    log_info "Vérification de l'existence d'une base de données existante..."
    
    if sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
        log_warning "Base de données $DB_NAME existe déjà"
        
        read -p "Voulez-vous la sauvegarder avant de la recréer? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            BACKUP_FILE="/tmp/${DB_NAME}_backup_$(date +%Y%m%d_%H%M%S).sql"
            log_info "Sauvegarde en cours vers $BACKUP_FILE..."
            
            sudo -u postgres pg_dump "$DB_NAME" > "$BACKUP_FILE"
            log_success "Sauvegarde créée: $BACKUP_FILE"
        fi
        
        read -p "Voulez-vous supprimer la base existante et continuer? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "Suppression de la base existante..."
            sudo -u postgres psql -c "DROP DATABASE IF EXISTS $DB_NAME;"
            sudo -u postgres psql -c "DROP USER IF EXISTS $ADMIN_USER;"
            sudo -u postgres psql -c "DROP USER IF EXISTS $APP_USER;"
            log_success "Base existante supprimée"
        else
            log_info "Déploiement annulé par l'utilisateur"
            exit 0
        fi
    fi
}

# Créer la base de données
create_database() {
    log_info "Création de la base de données..."
    
    if sudo -u postgres psql -f create_dcop413_database.sql; then
        log_success "Base de données créée avec succès"
    else
        log_error "Erreur lors de la création de la base de données"
        exit 1
    fi
}

# Insérer les données initiales
insert_initial_data() {
    log_info "Insertion des données initiales..."
    
    if sudo -u postgres psql -f insert_initial_data.sql; then
        log_success "Données initiales insérées avec succès"
    else
        log_error "Erreur lors de l'insertion des données initiales"
        exit 1
    fi
}

# Vérifier la création
verify_deployment() {
    log_info "Vérification du déploiement..."
    
    # Tester la connexion admin
    if sudo -u postgres psql -d "$DB_NAME" -U "$ADMIN_USER" -c "SELECT 'Connexion admin OK' AS test;" &> /dev/null; then
        log_success "Connexion administrateur fonctionnelle"
    else
        log_error "Problème de connexion administrateur"
        exit 1
    fi
    
    # Tester la connexion application
    if sudo -u postgres psql -d "$DB_NAME" -U "$APP_USER" -c "SELECT 'Connexion app OK' AS test;" &> /dev/null; then
        log_success "Connexion application fonctionnelle"
    else
        log_error "Problème de connexion application"
        exit 1
    fi
    
    # Vérifier les tables
    TABLE_COUNT=$(sudo -u postgres psql -d "$DB_NAME" -U "$ADMIN_USER" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'visiteurs_schema';")
    
    if [ "$TABLE_COUNT" -gt 10 ]; then
        log_success "Tables créées: $TABLE_COUNT tables trouvées"
    else
        log_error "Nombre de tables insuffisant: $TABLE_COUNT"
        exit 1
    fi
}

# Afficher les informations de connexion
display_connection_info() {
    echo -e "${GREEN}======================================================${NC}"
    echo -e "${GREEN}🎉 DÉPLOIEMENT TERMINÉ AVEC SUCCÈS!${NC}"
    echo -e "${GREEN}======================================================${NC}"
    echo
    echo -e "${BLUE}📋 INFORMATIONS DE CONNEXION:${NC}"
    echo
    echo -e "${YELLOW}Base de données:${NC} $DB_NAME"
    echo -e "${YELLOW}Host:${NC} localhost"
    echo -e "${YELLOW}Port:${NC} 5432"
    echo
    echo -e "${YELLOW}👤 Utilisateur Administrateur:${NC}"
    echo -e "   Nom: $ADMIN_USER"
    echo -e "   Mot de passe: DCOP413_SecurePass_2025!"
    echo -e "   Connexion: postgresql://$ADMIN_USER:DCOP413_SecurePass_2025!@localhost:5432/$DB_NAME"
    echo
    echo -e "${YELLOW}🔧 Utilisateur Application:${NC}"
    echo -e "   Nom: $APP_USER"
    echo -e "   Mot de passe: App_DCOP413_2025#Secure"
    echo -e "   Connexion: postgresql://$APP_USER:App_DCOP413_2025#Secure@localhost:5432/$DB_NAME"
    echo
    echo -e "${YELLOW}🔑 Comptes de Test:${NC}"
    echo -e "   Admin: admin / Admin_DCOP413_2025!"
    echo -e "   Agent: agent01 / Agent_DCOP413_2025!"
    echo -e "   Sécurité: securite01 / Securite_DCOP413_2025!"
    echo
    echo -e "${BLUE}🧪 COMMANDES DE TEST:${NC}"
    echo -e "   Connexion admin: psql -h localhost -U $ADMIN_USER -d $DB_NAME"
    echo -e "   Connexion app: psql -h localhost -U $APP_USER -d $DB_NAME"
    echo -e "   Lister les tables: \\dt visiteurs_schema.*"
    echo -e "   Voir les visiteurs: SELECT * FROM visiteurs_schema.visiteurs;"
    echo
    echo -e "${GREEN}✅ La base de données DCOP 413 est prête à l'emploi!${NC}"
}

# Fonction principale
main() {
    echo -e "${BLUE}Démarrage du déploiement...${NC}"
    
    check_postgresql
    check_sql_files
    backup_existing_database
    create_database
    insert_initial_data
    verify_deployment
    display_connection_info
    
    echo -e "${GREEN}🚀 Déploiement terminé avec succès!${NC}"
}

# Gestion des erreurs
trap 'log_error "Erreur détectée à la ligne $LINENO. Déploiement interrompu."; exit 1' ERR

# Exécution du script principal
main "$@"
