# 🗄️ Documentation Base de Données DCOP 413

## 📋 **Informations Essentielles**

### 🔐 **Détails de Connexion**
- **Nom de la BD** : `dcop413_visiteurs_db`
- **Host** : `localhost`
- **Port** : `5432`
- **Schéma** : `visiteurs_schema`

### 👥 **Comptes Utilisateurs**

#### **Administrateur Principal**
- **Utilisateur** : `dcop413_admin`
- **Mot de passe** : `DCOP413_SecurePass_2025!`
- **Privilèges** : Accès complet (SUPERUSER)
- **Usage** : Administration, maintenance, sauvegarde

#### **Utilisateur Application**
- **Utilisateur** : `dcop413_app`
- **Mot de passe** : `App_DCOP413_2025#Secure`
- **Privilèges** : SELECT, INSERT, UPDATE (limité)
- **Usage** : Application web, opérations courantes

#### **Comptes de Test**
- **Admin** : `admin` / `Admin_DCOP413_2025!`
- **Agent** : `agent01` / `Agent_DCOP413_2025!`
- **Sécurité** : `securite01` / `Securite_DCOP413_2025!`

## 🚀 **Déploiement Rapide**

### **Option 1 : Script Automatique (Recommandé)**
```bash
cd app_gestion_visiteurs/database
./deploy_database.sh
```

### **Option 2 : Déploiement Manuel**
```bash
# 1. Créer la base et les tables
sudo -u postgres psql -f create_dcop413_database.sql

# 2. Insérer les données initiales
sudo -u postgres psql -f insert_initial_data.sql
```

## 🔗 **Chaînes de Connexion**

### **Pour l'Administration**
```
postgresql://dcop413_admin:DCOP413_SecurePass_2025!@localhost:5432/dcop413_visiteurs_db
```

### **Pour l'Application**
```
postgresql://dcop413_app:App_DCOP413_2025#Secure@localhost:5432/dcop413_visiteurs_db
```

## 📊 **Structure de la Base de Données**

### **Tables Principales**

#### **1. utilisateurs**
Gestion des comptes système (admin, agents, sécurité)
```sql
SELECT * FROM visiteurs_schema.utilisateurs;
```

#### **2. visiteurs**
Informations personnelles des visiteurs
```sql
SELECT nom, prenom, nationalite, telephone1 FROM visiteurs_schema.visiteurs;
```

#### **3. visites**
Enregistrement des visites (programmées, en cours, terminées)
```sql
SELECT numero_visite, personne_rencontrer, date_visite, statut 
FROM visiteurs_schema.visites;
```

#### **4. organisations**
Entreprises et organismes des visiteurs
```sql
SELECT nom, type_organisation, niveau_confiance FROM visiteurs_schema.organisations;
```

#### **5. employes**
Personnel pouvant recevoir des visiteurs
```sql
SELECT nom, prenom, fonction, departement FROM visiteurs_schema.employes;
```

### **Tables de Sécurité**

#### **6. consignes_securite**
Règles de sécurité par visite
```sql
SELECT * FROM visiteurs_schema.consignes_securite;
```

#### **7. journal_audit**
Audit de toutes les modifications
```sql
SELECT table_name, action, horodatage FROM visiteurs_schema.journal_audit 
ORDER BY horodatage DESC LIMIT 10;
```

#### **8. sessions_utilisateurs**
Gestion des sessions actives
```sql
SELECT utilisateur_id, ip_adresse, created_at FROM visiteurs_schema.sessions_utilisateurs 
WHERE actif = true;
```

## 🔧 **Commandes Utiles**

### **Connexion à la Base**
```bash
# Connexion administrateur
psql -h localhost -U dcop413_admin -d dcop413_visiteurs_db

# Connexion application
psql -h localhost -U dcop413_app -d dcop413_visiteurs_db
```

### **Commandes PostgreSQL Essentielles**
```sql
-- Lister les tables
\dt visiteurs_schema.*

-- Décrire une table
\d visiteurs_schema.visiteurs

-- Voir les index
\di visiteurs_schema.*

-- Voir les vues
\dv visiteurs_schema.*

-- Voir les fonctions
\df visiteurs_schema.*

-- Quitter
\q
```

### **Requêtes de Monitoring**
```sql
-- Statistiques des visites par jour
SELECT * FROM visiteurs_schema.vue_stats_quotidiennes;

-- Visites complètes avec détails
SELECT * FROM visiteurs_schema.vue_visites_completes 
WHERE date_visite = CURRENT_DATE;

-- Sessions actives
SELECT COUNT(*) as sessions_actives 
FROM visiteurs_schema.sessions_utilisateurs 
WHERE actif = true;

-- Dernières activités d'audit
SELECT table_name, action, horodatage 
FROM visiteurs_schema.journal_audit 
ORDER BY horodatage DESC LIMIT 20;
```

## 🛡️ **Fonctionnalités de Sécurité**

### **Chiffrement des Mots de Passe**
```sql
-- Hasher un mot de passe
SELECT visiteurs_schema.hash_password('MonMotDePasse123!');

-- Vérifier un mot de passe
SELECT visiteurs_schema.verify_password('MonMotDePasse123!', hash_stocké);
```

### **Génération de Numéros de Visite**
```sql
-- Générer un numéro unique
SELECT visiteurs_schema.generate_numero_visite();
```

### **Nettoyage des Sessions**
```sql
-- Nettoyer les sessions expirées
SELECT visiteurs_schema.cleanup_expired_sessions();
```

## 📈 **Maintenance et Optimisation**

### **Sauvegarde**
```bash
# Sauvegarde complète
pg_dump -h localhost -U dcop413_admin dcop413_visiteurs_db > backup_$(date +%Y%m%d).sql

# Sauvegarde avec compression
pg_dump -h localhost -U dcop413_admin dcop413_visiteurs_db | gzip > backup_$(date +%Y%m%d).sql.gz
```

### **Restauration**
```bash
# Restaurer depuis une sauvegarde
psql -h localhost -U dcop413_admin dcop413_visiteurs_db < backup_20250703.sql
```

### **Optimisation**
```sql
-- Analyser les performances
ANALYZE;

-- Réindexer si nécessaire
REINDEX DATABASE dcop413_visiteurs_db;

-- Statistiques d'utilisation
SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del 
FROM pg_stat_user_tables 
WHERE schemaname = 'visiteurs_schema';
```

## 🔍 **Dépannage**

### **Problèmes de Connexion**
```bash
# Vérifier le statut de PostgreSQL
sudo systemctl status postgresql

# Redémarrer PostgreSQL
sudo systemctl restart postgresql

# Vérifier les connexions actives
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity WHERE datname = 'dcop413_visiteurs_db';"
```

### **Problèmes de Permissions**
```sql
-- Vérifier les privilèges
\dp visiteurs_schema.*

-- Réaccorder les privilèges si nécessaire
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA visiteurs_schema TO dcop413_app;
```

### **Logs et Monitoring**
```bash
# Voir les logs PostgreSQL
sudo tail -f /var/log/postgresql/postgresql-16-main.log

# Vérifier l'espace disque
df -h /var/lib/postgresql/
```

## 📞 **Support et Contact**

Pour toute question ou problème :
1. Consulter les logs d'audit dans `journal_audit`
2. Vérifier les sessions actives
3. Contrôler les permissions utilisateurs
4. Examiner les logs PostgreSQL

## 🔄 **Mise à Jour et Migration**

### **Ajout de Nouvelles Tables**
```sql
-- Se connecter en tant qu'admin
\c dcop413_visiteurs_db dcop413_admin

-- Créer la nouvelle table
CREATE TABLE visiteurs_schema.nouvelle_table (...);

-- Accorder les privilèges
GRANT SELECT, INSERT, UPDATE ON visiteurs_schema.nouvelle_table TO dcop413_app;
```

### **Modification de Structure**
```sql
-- Ajouter une colonne
ALTER TABLE visiteurs_schema.visiteurs ADD COLUMN nouvelle_colonne VARCHAR(100);

-- Créer un index
CREATE INDEX idx_nouvelle_colonne ON visiteurs_schema.visiteurs(nouvelle_colonne);
```

---

**Base de données DCOP 413 - Version 1.0**  
**Créée le :** 2025-07-03  
**Statut :** Production Ready 🚀
